# Unified Collection Implementation Plan for Extractors and Timelines

## Overview

This document outlines the implementation plan for migrating Extractors and Timelines objects from individual collections to a unified collection per workspace architecture. The plan leverages QdrantVectorStore from LangChain with built-in metadata filtering to isolate documents while maintaining backward compatibility for datasets created directly via API.

## Scope and Focus

**Target Objects:**
- **Extractors**: Objects that process documents and extract structured data
- **Timelines**: Objects that create timeline points, each backed by an extractor

**Backend Entities:**
- **Dataset**: Primary entity that groups related data files
- **DatasetIndex**: Index configuration entity connected directly to vector store collections

**Key Constraints:**
- Configuration changes implemented primarily on Dataset/DatasetIndex side
- Maintain backward compatibility for datasets created directly via API (not through extractors/timelines)
- **Vector Dimension Compatibility**: Qdrant collections have fixed vector dimensions set at creation time - different embedding models cannot coexist in the same collection

## Current State Analysis

### Current Architecture
- Each extractor creates its own collection via `auto_create_dataset_index()` using pattern: `{org.name}-{dataset.name}-{index.name}`
- Timeline points create individual extractors, each with separate dataset/index pairs
- Collections created through `index_namespace_suffix(org, dataset, index.name)` function
- Documents stored with metadata: `dataset_file_id`, `index_id`, `dataset_file_hash`
- No metadata filtering currently implemented in searches
- Custom `QdrantVectorStoreEmbedMetadata` extends LangChain's QdrantVectorStore for metadata embedding

### Key Files Involved
- `ira_chat/services/engine_manager.py` - Collection naming via `index_namespace_suffix()` and document indexing
- `ira_chat/api/extractor.py` - Extractor creation and `auto_create_dataset_index()` function
- `ira_chat/api/timeline.py` - Timeline point creation with extractor auto-creation
- `ira_chat/services/vectorstore.py` - Vectorstore initialization and collection naming utilities
- `ira_chat/services/qdrant_vs.py` - Custom QdrantVectorStore with metadata embedding
- `ira_chat/services/extraction.py` - Extraction processing and document retrieval

## Technical Requirements

### LangChain QdrantVectorStore Integration
- **Built-in Metadata Filtering**: Leverage `QdrantVectorStore.similarity_search(filter=models.Filter(...))`
- **Filter Structure**: Use `qdrant_client.models.Filter` with `must` conditions for precise filtering
- **Backward Compatibility**: Existing datasets continue using current collection mechanism

### Collection Naming Strategy
- **New Pattern**: `{org.name}-{workspace.name}-{vector_dimension}` for unified collections
- **Legacy Pattern**: `{org.name}-{dataset.name}-{index.name}` for direct API datasets
- **Configuration Flag**: `use_unified_collections` to control behavior per workspace
- **Vector Dimension Grouping**: Collections grouped by vector dimension to ensure compatibility

### Vector Dimension Constraint Analysis

**Problem**: Qdrant collections have a fixed vector dimension set at creation time. Different embedding models produce vectors of different dimensions:
- OpenAI `text-embedding-ada-002`: 1536 dimensions
- OpenAI `text-embedding-3-small`: 1536 dimensions
- OpenAI `text-embedding-3-large`: 3072 dimensions
- Ollama models: 4096 dimensions
- Google Generative AI: 768 dimensions
- Mistral AI: 1024 dimensions
- Voyage AI: 512-1024 dimensions

**Current System**: The `_embeddings_size()` function in `langchain_svc.py` already detects vector dimensions based on embedding model type.

**Solution**: Modify unified collection strategy to group by both workspace AND vector dimension, ensuring all documents in a collection have compatible vector dimensions.

### Modified Collection Strategy

**Dimension-Aware Unified Collections**:
- **Collection Pattern**: `{org.name}-{workspace.name}-{vector_dimension}`
- **Examples**:
  - `acme-marketing-1536` (OpenAI ada-002/3-small models)
  - `acme-marketing-3072` (OpenAI 3-large model)
  - `acme-marketing-4096` (Ollama models)
- **Metadata Filtering**: Same filtering strategy applies within each dimension-specific collection
- **Backward Compatibility**: Individual collections continue using existing naming

## Implementation Plan

### Stage 1: New Extractors/Timelines Use Unified Collections

#### 1.1 Update DatasetIndex Configuration (JSON Config Field)
**File:** `ira_chat/db/models.py`

**Changes:**
- Use existing `config` JSON field to store unified collection settings:
```python
# DatasetIndex.config structure for unified collections
{
    "index_type": "normal",
    "use_unified_collection": True,
    "collection_type": "UNIFIED_WORKSPACE",
    "workspace_id": 123,  # For reference
    "vector_dimension": 1536,  # Detected from embedding model
    "embedding_model": "text-embedding-ada-002"  # For dimension detection
}
```
- Update `DatasetIndex.get_config()` to return unified collection configuration
- No database schema changes required - leverage existing JSON field

#### 1.2 Update Collection Naming Logic
**File:** `ira_chat/services/engine_manager.py`

**Changes:**
- Modify `index_namespace_suffix()` to check config for `use_unified_collection` and include vector dimension:
```python
def index_namespace_suffix(org, dataset, index_name):
    # Get index config to check collection type
    if hasattr(dataset, 'indexes') and dataset.indexes:
        index_config = dataset.indexes[0].get_config()
        if index_config.get('use_unified_collection', False):
            vector_dim = index_config.get('vector_dimension', 1536)
            return f'{org.name}-{dataset.workspace.name}-{vector_dim}'

    # Legacy individual collection naming
    return f'{org.name}-{dataset.name}-{index_name}' if index_name else f'{org.name}-{dataset.name}'
```

- Add helper function to detect vector dimension:
```python
def get_vector_dimension_for_config(embedding_provider: str, embedding_model: str = None) -> int:
    """Detect vector dimension based on embedding provider and model."""
    # Use existing _embeddings_size logic from langchain_svc.py
    from ira_chat.services.langchain_svc import _embeddings_size
    from ira_chat.services.vectorstore import get_embedding_model

    # Create temporary embedding instance to get dimension
    temp_config = {'llm_type': embedding_provider}
    if embedding_model:
        temp_config['embedding_model'] = embedding_model

    embeddings = get_embedding_model(temp_config, 0, 0)  # Temp org/workspace IDs
    return _embeddings_size(embeddings)
```

#### 1.3 Enhanced Metadata Structure for New Objects
**File:** `ira_chat/services/engine_manager.py`

**Changes:**
- Update `index_file_for_extraction()` to add filtering metadata for unified collections:
```python
# Enhanced metadata for unified collections
if self._index.get_config().get('use_unified_collection', False):
    metadata.update({
        'extractor_id': extractor.id,
        'timeline_id': getattr(extractor, 'timeline_id', None),
        'subcollection_type': 'extractor',
        'subcollection_id': f'extractor-{extractor.id}',
        'workspace_id': self.ws.id
    })
```

#### 1.4 Update Extractor Creation Flow
**File:** `ira_chat/api/extractor.py`

**Changes:**
- Modify `auto_create_dataset_index()` to configure unified collections with vector dimension detection:
```python
# Detect vector dimension based on embedding provider
vector_dimension = get_vector_dimension_for_config(
    embedding_provider=llm_type,
    embedding_model=extractor_ws_config.get('embedding_model')
)

# Configure for unified collection when creating extractor datasets
index_config = {
    'index_type': engine_manager.INDEX_NORMAL,
    'use_unified_collection': True,
    'collection_type': 'UNIFIED_WORKSPACE',
    'workspace_id': ws.id,
    'vector_dimension': vector_dimension,
    'embedding_model': extractor_ws_config.get('embedding_model')
}
```

#### 1.5 Update Timeline Point Creation
**File:** `ira_chat/api/timeline.py`

**Changes:**
- Modify timeline point creation to use unified collections
- Update extractor creation in `create_point_timeline()` to set unified collection config
- Ensure timeline extractors inherit unified collection configuration

### Stage 2: Runtime Filtering for Retrieval and Search

#### 2.1 Create Metadata Filter Utilities
**File:** `ira_chat/services/vectorstore.py`

**New Functions:**
```python
from qdrant_client import models

def create_extractor_filter(extractor_id: int) -> models.Filter:
    """Create Qdrant filter for extractor-specific documents."""
    return models.Filter(
        must=[
            models.FieldCondition(
                key="extractor_id",
                match=models.MatchValue(value=extractor_id)
            )
        ]
    )

def create_timeline_filter(timeline_id: int) -> models.Filter:
    """Create Qdrant filter for timeline-specific documents."""
    return models.Filter(
        must=[
            models.FieldCondition(
                key="timeline_id",
                match=models.MatchValue(value=timeline_id)
            )
        ]
    )

def create_timeline_extractor_filter(timeline_id: int, extractor_id: int) -> models.Filter:
    """Create combined filter for specific timeline extractor."""
    return models.Filter(
        must=[
            models.FieldCondition(
                key="timeline_id",
                match=models.MatchValue(value=timeline_id)
            ),
            models.FieldCondition(
                key="extractor_id",
                match=models.MatchValue(value=extractor_id)
            )
        ]
    )
```

#### 2.2 Implement Filtered Retrieval in Engine Manager
**File:** `ira_chat/services/engine_manager.py`

**Changes:**
- Add method to get appropriate filter based on app type:
```python
def _get_metadata_filter(self):
    """Get metadata filter for current app context."""
    if not self._index.get_config().get('use_unified_collection', False):
        return None  # No filtering for individual collections

    if hasattr(self.app, 'id') and isinstance(self.app, models.Extractor):
        return create_extractor_filter(self.app.id)
    elif hasattr(self.app, 'id') and isinstance(self.app, models.Timeline):
        return create_timeline_filter(self.app.id)

    return None
```

- Update retrieval methods to apply filters:
```python
def _get_retriever(self, **kwargs):
    """Get retriever with appropriate metadata filtering."""
    search_kwargs = kwargs.get('search_kwargs', {})

    # Apply metadata filter for unified collections
    metadata_filter = self._get_metadata_filter()
    if metadata_filter:
        search_kwargs['filter'] = metadata_filter

    return self.vectorstore.as_retriever(
        search_type=kwargs.get('search_type', 'similarity'),
        search_kwargs=search_kwargs
    )
```

#### 2.3 Update Extraction Processing
**File:** `ira_chat/services/extraction.py`

**Changes:**
- Modify `_process_extractor()` to use filtered searches:
```python
async def _process_extractor(llm, vectorstore, ext_config, input=None, input_type='schema', access_type='session'):
    # Apply extractor-specific filtering for unified collections
    search_kwargs = {}
    if ext_config.get('use_unified_collection', False) and 'extractor_id' in ext_config:
        search_kwargs['filter'] = create_extractor_filter(ext_config['extractor_id'])

    # Use filtered search
    docs = vectorstore.similarity_search(input or "", k=5, **search_kwargs)
```

### Stage 3: Migration of Existing Extractors/Timelines

#### 3.1 Migration Strategy Overview
**Approach:** Migrate existing extractor and timeline collections to unified workspace collections while preserving all data and maintaining service availability.

**Key Principles:**
- Workspace-by-workspace migration to limit impact
- Data integrity validation at each step
- Rollback capability if issues arise
- Zero-downtime migration process

#### 3.2 Migration Script Implementation
**File:** `scripts/migrate_extractors_timelines_to_unified.py`

**Complete Implementation:**

```bash
python scripts/migrate_unified_collections.py --workspace-id 123
```

#### 3.2 Migration Script Implementation
**File:** `scripts/migrate_unified_collections.py`

**Key Features:**
- **All Workspaces Support**: Processes all workspaces in the system by default
- **Specific Workspace Option**: Can target a specific workspace with `--workspace-id`
- **Vector Dimension Detection**: Automatically detects and groups by vector dimensions
- **Dimension-Aware Collections**: Creates separate collections for different vector dimensions
- **Immediate Cleanup**: Deletes old collections right after successful migration
- **Fail-Fast Approach**: Stops on significant errors (database/connection issues)
- **Simplified Error Handling**: Only idempotent operations (like delete collection) use try/catch
- **Correct API Usage**: Uses proper DB API methods and Qdrant client operations

#### 3.3 Migration Execution Steps

**Step 1: Pre-Migration Validation**
```bash
# Run migration script in dry-run mode to assess impact for all workspaces
python scripts/migrate_unified_collections.py --dry-run

# Or for specific workspace
python scripts/migrate_unified_collections.py --workspace-id 123 --dry-run
```

**Step 2: Execute Migration**
```bash
# Migrate all workspaces
python scripts/migrate_unified_collections.py

# Or migrate specific workspace
python scripts/migrate_unified_collections.py --workspace-id 123
```

**Note:** Old collections are automatically cleaned up immediately after successful migration.

#### 3.3 Vector Dimension Handling in Migration

**Challenge**: Existing collections may not have vector dimension information stored in DatasetIndex config.

**Solution**: Detect vector dimensions from existing Qdrant collections during migration:

```python
async def detect_vector_dimension_from_index(index: models.DatasetIndex, vectorstore: Any) -> int:
    """Detect vector dimension from existing Qdrant collection."""
    # Get existing collection info
    source_collection_name = engine_manager.index_namespace_suffix(org, dataset, index.name)
    source_collection_full = ira_vectorstore.get_index_namespace(source_collection_name)

    client = vectorstore.client
    if client.collection_exists(source_collection_full):
        collection_info = client.get_collection(source_collection_full)
        return collection_info.config.params.vectors.size

    # Fallback: detect from embedding provider
    return get_vector_dimension_for_config(
        index.embedding_provider,
        index.get_config().get('embedding_model')
    )

async def group_indexes_by_vector_dimension(
    indexes: List[models.DatasetIndex],
    vectorstore: Any
) -> Dict[int, List[models.DatasetIndex]]:
    """Group indexes by their vector dimensions."""
    dimension_groups = {}

    for index in indexes:
        vector_dim = await detect_vector_dimension_from_index(index, vectorstore)
        if vector_dim not in dimension_groups:
            dimension_groups[vector_dim] = []
        dimension_groups[vector_dim].append(index)

    return dimension_groups
```

**Migration Process Updates**:
1. **Group by Dimension**: Before migration, group all indexes by vector dimension
2. **Create Dimension-Specific Collections**: Create separate unified collections for each dimension
3. **Migrate by Dimension**: Process each dimension group separately
4. **Update Configs**: Store detected vector dimension in DatasetIndex config

## Vector Dimension Constraint Solutions

### Q1: How should we handle multiple vector dimensions within a single workspace?

**Answer**: Create separate unified collections for each vector dimension within the workspace.

**Implementation**:
- **Collection Pattern**: `{org.name}-{workspace.name}-{vector_dimension}`
- **Examples**:
  - `acme-marketing-1536` (OpenAI ada-002, 3-small)
  - `acme-marketing-3072` (OpenAI 3-large)
  - `acme-marketing-4096` (Ollama models)
- **Benefits**: Maintains unified collection benefits while ensuring vector compatibility

### Q2: Should we modify the unified collection strategy to group by vector dimension rather than just workspace?

**Answer**: Yes, group by BOTH workspace AND vector dimension.

**Rationale**:
- **Technical Requirement**: Qdrant collections must have consistent vector dimensions
- **Logical Grouping**: Workspace provides logical isolation, dimension ensures technical compatibility
- **Scalability**: Supports multiple embedding models within the same workspace
- **Future-Proof**: Accommodates new embedding models with different dimensions

### Q3: What would be the naming convention for dimension-specific collections?

**Answer**: `{org.name}-{workspace.name}-{vector_dimension}`

**Examples**:
```
# Current individual collections
acme-extractor-dataset-123-openai
acme-timeline-dataset-456-openai

# New unified collections (dimension-aware)
acme-marketing-1536    # All 1536-dim vectors (ada-002, 3-small)
acme-marketing-3072    # All 3072-dim vectors (3-large)
acme-support-1536      # Different workspace, same dimension
acme-support-768       # Google Generative AI embeddings
```

**Benefits**:
- **Clear Identification**: Dimension is explicit in collection name
- **Easy Management**: Simple to identify compatible collections
- **Debugging**: Vector dimension issues are immediately apparent

### Q4: How would this affect the metadata filtering strategy and migration script?

**Metadata Filtering**: No changes required - same filtering strategy applies within each dimension-specific collection.

**Migration Script Changes**:
1. **Dimension Detection**: Add functions to detect vector dimensions from existing collections
2. **Grouping Logic**: Group indexes by vector dimension before migration
3. **Multiple Target Collections**: Create separate unified collections for each dimension
4. **Enhanced Validation**: Ensure all documents in a collection have compatible dimensions

### Q5: Should we add vector dimension detection to the DatasetIndex configuration?

**Answer**: Yes, add vector dimension to DatasetIndex config for new indexes and detect/store during migration for existing ones.

**Implementation**:
```python
# Enhanced DatasetIndex.config
{
    "index_type": "normal",
    "use_unified_collection": True,
    "collection_type": "UNIFIED_WORKSPACE",
    "workspace_id": 123,
    "vector_dimension": 1536,  # NEW: Detected/configured dimension
    "embedding_model": "text-embedding-ada-002",  # For reference
    "embedding_provider": "openai"  # For dimension detection
}
```

**Benefits**:
- **Explicit Configuration**: Vector dimension is clearly documented
- **Migration Support**: Enables proper grouping during migration
- **Validation**: Can validate compatibility before document insertion
- **Debugging**: Easier to troubleshoot dimension-related issues

#### 3.4 Post-Migration Verification

**Validation Checklist:**
- [ ] All documents migrated with correct metadata
- [ ] Extractor searches return only relevant documents
- [ ] Timeline searches work correctly across timeline points
- [ ] Performance metrics within acceptable ranges
- [ ] No data loss or corruption detected

**Rollback Procedure:**
If issues are detected, the migration can be rolled back by:
1. Restoring original collections from backup
2. Updating DatasetIndex configs to remove unified collection flags
3. Reverting code changes if necessary

## Implementation Details

### Enhanced Metadata Structure for Unified Collections
```python
# Enhanced document metadata for filtering
document_metadata = {
    # Core identification
    'workspace_id': workspace.id,
    'extractor_id': extractor.id,
    'timeline_id': timeline.id,  # For timeline extractors

    # Subcollection identification
    'subcollection_type': 'extractor',  # or 'timeline'
    'subcollection_id': f'extractor-{extractor.id}',

    # Vector dimension information
    'vector_dimension': 1536,  # Ensures compatibility within collection
    'embedding_model': 'text-embedding-ada-002',  # For reference

    # Existing metadata (preserved)
    'dataset_file_id': file.id,
    'index_id': index.id,
    'dataset_file_hash': file.hash,

    # Additional context
    'source': file.name,
    'created_at': datetime.utcnow().isoformat()
}
```

## Benefits and Advantages

### Resource Efficiency
- **Reduced Qdrant Overhead**: Fewer collections per workspace-dimension combination reduces memory and CPU usage
- **Simplified Monitoring**: Limited collections per workspace (typically 1-3 based on embedding models used)
- **Storage Optimization**: Better resource utilization across workspace documents with compatible dimensions
- **Dimension Compatibility**: Ensures all vectors in a collection are compatible, preventing runtime errors

### Enhanced Functionality
- **Cross-Extractor Queries**: Metadata filtering enables searches across multiple extractors
- **Timeline Coherence**: Timeline documents can be queried as a cohesive unit
- **Flexible Filtering**: LangChain's built-in filtering supports complex query patterns

### Operational Benefits
- **Backward Compatibility**: Existing direct API datasets continue unchanged
- **Gradual Migration**: Controlled rollout minimizes disruption
- **Future Extensibility**: Framework supports additional object types (chats, etc.)

## Risk Mitigation and Safeguards

### Data Integrity Protection
- **Comprehensive Testing**: Validate filtering accuracy before migration
- **Incremental Migration**: Process workspaces individually to limit blast radius
- **Data Verification**: Compare document counts and content before/after migration
- **Rollback Capability**: Maintain ability to revert to individual collections

### Performance Considerations
- **Metadata Indexing**: Ensure Qdrant properly indexes filtering fields
- **Query Performance**: Monitor search latency with metadata filters
- **Collection Size Limits**: Monitor unified collection sizes and performance impact
- **Load Testing**: Validate performance under realistic workloads

## Success Criteria and Validation

### Functional Requirements
- [ ] **New Extractors**: Use dimension-aware unified collections with proper metadata filtering
- [ ] **New Timelines**: Timeline points create extractors using unified collections grouped by vector dimension
- [ ] **Vector Dimension Detection**: Automatic detection and configuration of vector dimensions
- [ ] **Dimension Compatibility**: All documents in a collection have compatible vector dimensions
- [ ] **Metadata Filtering**: Documents correctly isolated by extractor_id and timeline_id within dimension-specific collections
- [ ] **Backward Compatibility**: Direct API datasets continue using individual collections
- [ ] **Migration Integrity**: Existing extractors/timelines migrated without data loss, grouped by vector dimension
- [ ] **Search Accuracy**: Filtered searches return only relevant documents from compatible vector spaces

### Performance Requirements
- [ ] **Search Latency**: Metadata filtering adds <50ms to search time
- [ ] **Collection Efficiency**: Reduced collection count per workspace (target: 80% reduction)
- [ ] **Memory Usage**: Qdrant memory usage reduced due to fewer collections
- [ ] **Throughput**: Document indexing performance maintained or improved

### Operational Requirements
- [ ] **Migration Safety**: Zero-downtime migration process
- [ ] **Monitoring**: Collection health and performance metrics available
- [ ] **Rollback Capability**: Ability to revert to individual collections if needed
- [ ] **Documentation**: Updated technical documentation and migration guides

## Implementation Timeline

### Stage 1: New Objects Use Unified Collections (Weeks 1-2)
- **Week 1**:
  - Update DatasetIndex config handling to use JSON field
  - Modify collection naming logic in `index_namespace_suffix()`
  - Implement enhanced metadata structure for new documents
- **Week 2**:
  - Update extractor creation flow in `auto_create_dataset_index()`
  - Modify timeline point creation to use unified collections
  - Add configuration flags and validation
- **Validation**: Ensure new extractors/timelines create and use unified collections correctly

### Stage 2: Runtime Filtering Implementation (Weeks 3-4)
- **Week 3**:
  - Implement metadata filter utilities using LangChain QdrantVectorStore
  - Update engine manager to apply filters based on app context
  - Modify retrieval methods to use filtered searches
- **Week 4**:
  - Update extraction processing to use filtered document retrieval
  - Implement timeline-specific filtering for timeline extractors
  - Add performance monitoring and validation
- **Validation**: Verify filtering works correctly for both unified and individual collections

### Stage 3: Migration of Existing Objects (Weeks 5-8)
- **Week 5**:
  - Complete migration script development and testing
  - Implement data validation and rollback procedures
  - Test migration on development/staging environments
- **Week 6-7**:
  - Execute workspace-by-workspace production migration
  - Monitor performance and data integrity during migration
  - Handle any issues or edge cases discovered
- **Week 8**:
  - Post-migration validation and performance optimization
  - Clean up old collections after verification
  - Update documentation and finalize implementation

**Total Timeline**: 8 weeks for complete implementation and migration

## Context7 Documentation References

This implementation leverages the following LangChain QdrantVectorStore capabilities:

1. **Metadata Filtering**: `QdrantVectorStore.similarity_search(filter=models.Filter(...))`
2. **Filter Construction**: `qdrant_client.models.Filter` with `must` conditions
3. **Field Conditions**: `models.FieldCondition` for exact matches
4. **Collection Management**: Built-in collection creation and management
5. **Document Addition**: `add_documents()` with metadata preservation

The implementation follows LangChain best practices for vector store integration and leverages Qdrant's native filtering capabilities for optimal performance.
