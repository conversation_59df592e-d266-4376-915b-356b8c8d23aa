import asyncio
import tqdm

from ira_chat.client import api_client


async def main():
    token = '60a2d7ce-9ae3-4f06-bf65-9fdedc9679d2'
    api_url = 'https://isc.crm-builder.kibernetika.io'
    workspace_id = 1
    client = api_client.APIClient(token, api_url)

    client.set_workspace(workspace_id)

    timelines = await client.atimeline_list(limit=10000)
    print(f'Found {len(timelines)} timelines')
    for timeline in tqdm.tqdm(timelines):
        await client.atimeline_delete(timeline['id'])
        # print(f'Deleted timeline {timeline["id"]}')

    extractors = await client.aextractor_list(limit=10000)
    print(f'Found {len(extractors)} extractors')
    for extractor in tqdm.tqdm(extractors):
        await client.aextractor_delete(extractor['id'])
        # print(f'Deleted extractor {extractor["id"]}')


if __name__ == '__main__':
    asyncio.run(main())
