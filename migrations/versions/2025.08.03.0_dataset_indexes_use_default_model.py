"""dataset indexes use default model

Revision ID: 2025.08.03.0
Revises: 2025.06.23.0
Create Date: 2025-08-03 10:41:46.289530

"""
import json
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.08.03.0'
down_revision: Union[str, None] = '2025.06.23.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()
    all_indexes = conn.execute(sa.text("SELECT id, embedding_provider, config FROM dataset_indexes;")).fetchall()
    for index in all_indexes:
        config = index[2] or {}
        embedding_provider = index[1] or 'openai'
        if embedding_provider in ['openai', 'azure_openai']:
            embedding_model = config.get('embedding_model')
            if embedding_model:
                continue
            config['embedding_model'] = 'text-embedding-ada-002'
            config_str = json.dumps(config)
            conn.execute(sa.text("UPDATE dataset_indexes SET config = :config_str WHERE id = :id"), {
                'config_str': config_str,
                'id': index[0]
            })

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
