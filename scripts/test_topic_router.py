#!/usr/bin/env python
"""
Test script for the topic_router_node function.

This script creates a test configuration with topic groups and runs the topic router
with different test queries to see how it routes them.
"""

import asyncio
import os
import argparse

import pandas as pd
import tqdm
from langchain_openai import ChatOpenAI, AzureChatOpenAI

from ira_chat.services.agents import (
    topic_router_node_fn,
    PATH_NO_ANSWER, PATH_NEXT
)
from ira_chat.utils import utils


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('--llm', default='openai')
    parser.add_argument('-f', '--file')
    return parser.parse_args()


async def main():
    """Test the topic router with different queries."""
    # Create a mock LLM
    args = parse_args()
    utils.setup_logging()
    llm_type = args.llm
    if llm_type == 'openai':
        llm = ChatOpenAI(
            model='gpt-4o-mini',
            temperature=0.5
            # model='o4-mini',
            # temperature=1,
        )
    elif llm_type == 'azure_openai':
        llm = AzureChatOpenAI(
            model='gpt-4o-mini',
            azure_deployment='gpt-4o-mini',
            # azure_endpoint='https://isc.openai.azure.com/',
            azure_endpoint='https://isc-eastus-2.openai.azure.com/',
            api_version='2024-12-01-preview',
            api_key=os.environ['AZURE_OPENAI_API_KEY'],
            temperature=0.5
        )
    else:
        raise ValueError(f'Unknown LLM type: {llm_type}')

    # Define test configuration with topic groups
    config = {
        "topic_router_enabled": True,
        "topic_router_groups": {
            "computer help": {
                "path": PATH_NO_ANSWER,
                "topics": [
                    "invoices (factures)",
                    "sales (ventes), pricing (tarif)",
                    "billing (facturation), TVA",
                    "calendar issues",
                    "computer issues",
                    "printer issue",
                    "scanner issue",
                    "serialisation",
                    "payments",
                    "document printing (imprimante)",
                    "computer security",
                    "carte bancaire (CB)",
                    "carte vitale (CV)",
                    "PAX lecteur",
                    "télétransmission (télétrans)",
                    "batch tracking (batch issues)",
                    "system operations",
                    "Windows",
                    "hardware",
                    "QR code"
                ]
            },
            "WinAutopilote": {
                "path": PATH_NEXT,
                "topics": [
                    "WinAutopilote",
                    "WAP",
                    "pharma ML",
                    "labs",
                    "code merging",
                    "order and product issues (commandes)",
                    "OCP / CERP",
                    "Les génériques"
                ]
            }
        }
    }

    # Create the topic router node
    topic_router = topic_router_node_fn(llm, config)

    df = pd.read_csv(args.file)
    test_queries = df['question'].tolist()
    tags = df['tags'].tolist()

    # Run the topic router with each query
    print("\n===== TOPIC ROUTER TEST RESULTS =====\n")

    pbar = tqdm.tqdm(total=len(test_queries))

    async def inner(state):
        try:
            res = await topic_router(state)
        except Exception as e:
            print(f"Error: {e}, {state['question']}")
            raise
        pbar.update(1)
        return res

    tasks = []
    for query in test_queries:
        # Create a state with the query
        state = {"question": query}

        # Run the topic router
        task = asyncio.create_task(inner(state))
        tasks.append(task)

    results = await asyncio.gather(*tasks)
    df = pd.DataFrame(results)
    print()
    print(df['topic_group'].value_counts())

    group_tag_map = {
        'WinAutopilote': 'WAP',
        'computer help': 'Not WAP',
        'no_answer': 'Not WAP',
        'call_me_back': 'Not WAP',
        'unclear_question': 'Not WAP',
        'next': 'WAP',
        'none': 'WAP',
        '': 'WAP',
        'other': 'WAP',
        'clarify': 'Not WAP'
    }
    df['group_tag'] = df['topic_group'].map(group_tag_map)
    df['ground_truth'] = tags
    df['correct'] = df['group_tag'] == df['ground_truth']
    df['question'] = test_queries
    print()
    print()
    print(df[['correct', 'ground_truth']].value_counts())

    # Output incorrect pairs
    print()
    print(df[~df['correct']][['question', 'ground_truth', 'group_tag', 'topic_group', 'topic']])
    # Save to file
    df.to_csv('topic_router_results.csv', index=False)


if __name__ == "__main__":
    asyncio.run(main())
