
import asyncio

from ira_chat.db import base
from ira_chat.db import api as db_api


async def main():
    async with base.session_context():
        workspaces = await db_api.list_workspaces()
        workspace_map = {ws.id: ws for ws in workspaces}
        extractors, _ = await db_api.list_extractors()
        extractor_map = {ext.auto_dataset_id: ext for ext in extractors}
        indexes = await db_api.list_dataset_indexes()
        for index in indexes:
            extractor = extractor_map.get(index.dataset_id)
            if not extractor:
                continue
            print(f'Updating index {index.name} for dataset {index.dataset_id}')
            config = index.get_config()
            ws = workspace_map[extractor.workspace_id]
            config['workspace_id'] = ws.id
            config['workspace_name'] = ws.name
            await db_api.update_dataset_index(index, {'config': config})


if __name__ == '__main__':
    asyncio.run(main())
