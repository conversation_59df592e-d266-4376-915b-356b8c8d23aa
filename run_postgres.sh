arg=$1
name=postgres
password=${PGPASSWORD:-password}

local_port=5432
INIT_ARGS="-c max_connections=100"

status=$(docker inspect $name | grep Status | cut -d '"' -f 4)

if [ ! -z "$status" ]; then
  if [ "$status" != "running" ]; then
    docker start $name
  fi
  echo "Already running."
  exit 0
else
  echo "Container not found; attempt to start"
fi

if [[ "$arg" == '-i' ]]; then
  docker run -p $local_port:5432 --name $name -e POSTGRES_PASSWORD=$password -e POSTGRES_INITDB_ARGS="$INIT_ARGS" -e PGDATA=/var/lib/postgresql/data/pgdata \
   --rm -it -v $HOME/pgdata:/var/lib/postgresql/data/pgdata:z postgres:16.6
else
  docker run -p $local_port:5432 --name $name -e POSTGRES_PASSWORD=$password -e POSTGRES_INITDB_ARGS="$INIT_ARGS" -e PGDATA=/var/lib/postgresql/data/pgdata \
   -d -v $HOME/pgdata:/var/lib/postgresql/data/pgdata:z postgres:16.6
fi
