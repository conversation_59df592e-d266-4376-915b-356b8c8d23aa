#!/usr/bin/env python3
"""
Test script to verify the async batch grading implementation.
"""

import asyncio
import time
from langchain_core.documents import Document

# Import the functions we want to test
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ira_chat.services.agents import (
    BatchGrade, 
    DocumentGrade,
)


def test_async_batch_processing():
    """Test that async batch processing works correctly."""
    print("Testing async batch processing logic...")
    
    # Simulate the batch processing logic
    documents = []
    for i in range(12):  # 12 documents
        doc = Document(
            page_content=f"This is test document {i+1} content.",
            metadata={
                'source': f'test_doc_{i+1}.txt',
                'page': i+1,
                '_id': f'doc_{i+1}'
            }
        )
        documents.append(doc)
    
    batch_size = 5
    
    # Simulate creating async tasks
    async_tasks = []
    batch_metadata = []
    
    for batch_start in range(0, len(documents), batch_size):
        batch_end = min(batch_start + batch_size, len(documents))
        batch_docs = documents[batch_start:batch_end]
        
        # Simulate creating an async task (we'll just store the batch info)
        async_tasks.append(f"task_for_batch_{batch_start}_{batch_end}")
        batch_metadata.append({
            'batch_start': batch_start,
            'batch_end': batch_end,
            'batch_docs': batch_docs,
            'batch_size': len(batch_docs)
        })
    
    print(f"Created {len(async_tasks)} async tasks for {len(documents)} documents")
    print("Batch metadata:")
    for i, meta in enumerate(batch_metadata):
        print(f"  Batch {i+1}: docs {meta['batch_start']}-{meta['batch_end']-1} (size: {meta['batch_size']})")
    
    # Verify we have the right number of batches
    expected_batches = (len(documents) + batch_size - 1) // batch_size  # Ceiling division
    assert len(async_tasks) == expected_batches
    assert len(batch_metadata) == expected_batches
    
    # Verify all documents are covered
    total_docs_in_batches = sum(meta['batch_size'] for meta in batch_metadata)
    assert total_docs_in_batches == len(documents)
    
    print("✓ Async batch processing logic works correctly")


def test_batch_grade_models():
    """Test that the batch grading models work correctly."""
    print("\nTesting batch grading models...")
    
    # Test creating a batch grade response
    batch_grade = BatchGrade(
        document_grades=[
            DocumentGrade(document_index=0, reasoning="Relevant content", score=8),
            DocumentGrade(document_index=1, reasoning="Very relevant", score=9),
            DocumentGrade(document_index=2, reasoning="Not relevant", score=3),
            DocumentGrade(document_index=3, reasoning="Moderately relevant", score=6),
            DocumentGrade(document_index=4, reasoning="Highly relevant", score=7),
        ]
    )
    
    assert len(batch_grade.document_grades) == 5
    
    # Test processing the results (simulate the processing logic)
    score_threshold = 5
    relevant_docs = []
    
    for doc_grade in batch_grade.document_grades:
        if doc_grade.score > score_threshold:
            relevant_docs.append({
                'index': doc_grade.document_index,
                'score': doc_grade.score,
                'reasoning': doc_grade.reasoning
            })
    
    print(f"Found {len(relevant_docs)} relevant documents out of {len(batch_grade.document_grades)}")
    for doc in relevant_docs:
        print(f"  Doc {doc['index']}: score={doc['score']}, reasoning='{doc['reasoning']}'")
    
    # Should have 4 relevant docs (scores 8, 9, 6, 7)
    assert len(relevant_docs) == 4
    
    print("✓ Batch grading models work correctly")


def test_performance_comparison():
    """Test performance comparison between individual and batch approaches."""
    print("\nTesting performance comparison...")
    
    scenarios = [
        {"docs": 10, "batch_size": 3},
        {"docs": 15, "batch_size": 5},
        {"docs": 20, "batch_size": 8},
        {"docs": 25, "batch_size": 10},
    ]
    
    print("Performance comparison:")
    print("Documents | Batch Size | Individual Calls | Batch Calls | Reduction")
    print("----------|------------|------------------|-------------|----------")
    
    for scenario in scenarios:
        docs = scenario["docs"]
        batch_size = scenario["batch_size"]
        
        # Individual approach: 1 call per document
        individual_calls = docs
        
        # Batch approach: ceiling division
        batch_calls = (docs + batch_size - 1) // batch_size
        
        reduction = individual_calls - batch_calls
        percentage = (reduction / individual_calls) * 100
        
        print(f"{docs:9d} | {batch_size:10d} | {individual_calls:16d} | {batch_calls:11d} | {reduction:3d} ({percentage:4.1f}%)")
        
        # Verify we always reduce calls when batch_size > 1
        assert batch_calls <= individual_calls
        if batch_size > 1:
            assert batch_calls < individual_calls
    
    print("✓ Performance comparison shows expected improvements")


async def test_async_gather_simulation():
    """Test that asyncio.gather simulation works."""
    print("\nTesting asyncio.gather simulation...")
    
    async def mock_llm_call(batch_id, delay=0.1):
        """Simulate an LLM call with some delay."""
        await asyncio.sleep(delay)
        return f"result_for_batch_{batch_id}"
    
    # Create multiple async tasks
    tasks = []
    for i in range(5):
        tasks.append(mock_llm_call(i))
    
    # Measure time for concurrent execution
    start_time = time.time()
    results = await asyncio.gather(*tasks)
    end_time = time.time()
    
    concurrent_time = end_time - start_time
    
    # Measure time for sequential execution
    start_time = time.time()
    sequential_results = []
    for i in range(5):
        result = await mock_llm_call(i)
        sequential_results.append(result)
    end_time = time.time()
    
    sequential_time = end_time - start_time
    
    print(f"Concurrent execution: {concurrent_time:.3f}s")
    print(f"Sequential execution: {sequential_time:.3f}s")
    print(f"Speedup: {sequential_time/concurrent_time:.1f}x")
    
    # Verify results are the same
    assert len(results) == len(sequential_results)
    assert results == sequential_results
    
    # Concurrent should be faster (allowing for some variance)
    assert concurrent_time < sequential_time * 0.8
    
    print("✓ Asyncio.gather provides expected performance benefits")


async def main():
    """Run all tests."""
    print("=== Async Batch Grading Test ===\n")
    
    try:
        test_async_batch_processing()
        test_batch_grade_models()
        test_performance_comparison()
        await test_async_gather_simulation()
        
        print("\n=== Test Summary ===")
        print("✓ All tests passed!")
        print("✓ Async batch processing logic is correct")
        print("✓ Batch grading models work as expected")
        print("✓ Performance improvements are achievable")
        print("✓ Asyncio.gather provides concurrency benefits")
        print("\n🎉 Async batch document grading implementation is ready!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
