import contextlib
import datetime
import logging
import os
from typing import Optional

import sqlalchemy.pool
from fastapi import HTTPException
from sqlalchemy import create_engine
from sqlalchemy import delete, update
from sqlalchemy import exc
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy.orm import sessionmaker as sessionmaker_sync

from ira_chat.context import context
from ira_chat.exceptions import NotFoundException
from ira_chat.utils import utils

logger = logging.getLogger(__name__)
_ENGINE = None
_ENGINE_SYNC = None
_SESSION_NAME = 'session'


class _ModelBase(object):
    """Base class for all Mistral SQLAlchemy DB Models."""

    __table__ = None

    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)

    def update(self, values):
        """Make the model object behave like a dict."""
        for k, v in values.items():
            setattr(self, k, v)

    def to_dict(self):
        """sqlalchemy based automatic to_dict method."""
        d = {}

        for col in self.__table__.columns:
            if col.name and hasattr(self, col.name):
                v = getattr(self, col.name)
                if isinstance(v, datetime.datetime):
                    d[col.name] = date_to_string(v)
                elif isinstance(v, datetime.date):
                    d[col.name] = dt_to_string(v)
                else:
                    d[col.name] = v

        return d

    def __repr__(self):
        return '%s %s' % (type(self).__name__, self.to_dict().__repr__())


def date_to_string(date: datetime.datetime):
    if not date:
        return None
    return datetime.datetime.strftime(date, '%Y-%m-%d %H:%M:%S')


def dt_to_string(date: datetime.date):
    if not date:
        return None
    return date.isoformat()


ModelBase = declarative_base(cls=_ModelBase)


# ModelBase = declarative.declarative_base(cls=_ModelBase)


def get_engine():
    global _ENGINE

    if not _ENGINE:
        debug_db = os.environ.get('DEBUG_DB', 'false').lower() == 'true'
        logging.getLogger('sqlalchemy.engine').propagate = False
        _ENGINE = create_async_engine(
            utils.generate_connection_string(),
            echo=debug_db,
            pool_pre_ping=True,
            pool_size=int(os.environ.get('DB_POOL_SIZE') or 10),
            pool_timeout=20,
            max_overflow=int(os.environ.get('DB_MAX_OVERFLOW') or 5),
            poolclass=sqlalchemy.pool.AsyncAdaptedQueuePool,
            pool_recycle=300,
        )

    return _ENGINE


def get_sync_engine():
    global _ENGINE_SYNC

    if not _ENGINE_SYNC:
        debug_db = os.environ.get('DEBUG_DB', 'false').lower() == 'true'
        logging.getLogger('sqlalchemy.engine').propagate = False
        _ENGINE_SYNC = create_engine(
            utils.generate_connection_string('psycopg2'),
            echo=debug_db,
            pool_pre_ping=True,
            pool_recycle=60,
            pool_size=int(os.environ.get('DB_POOL_SIZE') or 3),
            pool_timeout=20,
            max_overflow=int(os.environ.get('DB_MAX_OVERFLOW') or 1),
        )

    return _ENGINE_SYNC


def get_session_sync():
    session_maker = sessionmaker_sync(
        bind=get_sync_engine(),
        expire_on_commit=False,
    )

    # return orm.scoped_session(session_maker)
    return session_maker()


def get_session() -> AsyncSession:
    session_maker = sessionmaker(
        bind=get_engine(),
        expire_on_commit=False,
        class_=AsyncSession,
    )

    # return orm.scoped_session(session_maker)
    return session_maker()


def _get_thread_local_session():
    return context.db.value


def _get_or_create_thread_local_session():
    ses = _get_thread_local_session()

    if ses:
        return ses, False

    ses = get_session()

    _set_thread_local_session(ses)

    return ses, True


def _set_thread_local_session(session):
    context.db.set(session)
    # utils.set_thread_local(_DB_SESSION_THREAD_LOCAL_NAME, session)


def session_aware(param_name: Optional[str] = "session"):
    """Decorator for methods working within db session."""

    def _decorator(func):
        async def _within_session(*args, **kw):
            # If 'created' flag is True it means that the transaction is
            # demarcated explicitly outside this module.
            ses, created = _get_or_create_thread_local_session()

            try:
                kw[param_name] = ses

                result = await func(*args, **kw)

                if created:
                    await ses.commit()

                return result
            except Exception as e:
                if created:
                    await ses.rollback()
                if 'duplicate key value violates unique constraint' in str(e):
                    logger.error(f"Duplicate entry for inserting item: {e}")
                    raise HTTPException(409, "Duplicate entry for inserting item")
                raise
            finally:
                if created:
                    _set_thread_local_session(None)
                    await ses.close()

        _within_session.__doc__ = func.__doc__

        return _within_session

    return _decorator


@contextlib.asynccontextmanager
async def session_context():
    """Context manager for methods working within db session."""
    # If 'created' flag is True it means that the transaction is
    # demarcated explicitly outside this module.
    ses, created = _get_or_create_thread_local_session()

    try:
        yield ses
        if created:
            await ses.commit()
    except Exception as e:
        if created:
            await ses.rollback()
        if 'duplicate key value violates unique constraint' in str(e):
            logger.error(f"Duplicate entry for inserting item: {e}")
            raise HTTPException(409, "Duplicate entry for inserting item")
        raise
    finally:
        if created:
            _set_thread_local_session(None)
            await ses.close()


def session_aware_sync(param_name="session"):
    """Decorator for methods working within db session."""

    def _decorator(func):
        def _within_session(*args, **kw):
            # If 'created' flag is True it means that the transaction is
            # demarcated explicitly outside this module.
            ses = context.db.value

            if ses:
                created = False
            else:
                ses = get_session_sync()
                context.db.set(ses)
                created = True

            try:
                kw[param_name] = ses

                result = func(*args, **kw)

                if created:
                    ses.commit()

                return result
            except Exception:
                if created:
                    ses.rollback()
                raise
            finally:
                if created:
                    context.db.set(None)
                    ses.close()

        _within_session.__doc__ = func.__doc__

        return _within_session

    return _decorator


# @session_aware()
def model_query(model, session=None):
    """Query helper.

    :param model: base model to query
    """
    return select(model)


@session_aware()
async def create_model(model, values, session=None):
    if isinstance(values, model):
        instance = values
    else:
        instance = model(**values)

    try:
        session.add(instance)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            f"Duplicate entry for {model.__name__}: %s" % e
        )

    return instance


@session_aware()
async def get_by_id(model, id: int | str, session=None):
    q = select(model).where(model.id == id)
    # res = await session.get(models.Chat, id)
    res = await session.execute(q)
    res = res.scalar()

    if not res:
        raise NotFoundException(f'{model.__name__} not found for id: {id}')
    return res


@session_aware()
async def delete_by_id(model, id: int | str, session=None):
    delete_q = delete(model).where(model.id == id)
    res = await session.execute(delete_q)
    return res.rowcount


@session_aware()
async def mark_as_delete(model, id: int | str, session=None):
    values = {'deleted_at': now()}
    update_q = update(model).where(model.id == id)
    update_q = update_q.values(values)

    return await session.execute(update_q)


def now():
    return datetime.datetime.now(datetime.timezone.utc)
