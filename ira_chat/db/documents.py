from typing import Sequence

from sqlalchemy import delete, text
from sqlalchemy.future import select

from ira_chat.db import base
from ira_chat.db import models


@base.session_aware()
async def create_document(values: dict, session=None):
    return await base.create_model(models.Document, values, session=session)


@base.session_aware()
async def list_documents(file_id: int = None, ids: Sequence[str] = None, session=None):
    query = select(models.Document)
    if file_id:
        query = query.where(models.Document.file_id == file_id)
    if ids:
        query = query.where(models.Document.id.in_(ids))
    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def delete_documents(file_id: int, session=None):
    delete_q = delete(models.Document).where(models.Document.file_id == file_id)
    await session.execute(delete_q)


@base.session_aware()
async def list_documents_by_ids_async_order(ids: Sequence[str], session=None):
    if not ids:
        return []
    raw = text(
        "SELECT * FROM documents d "
        f"JOIN UNNEST(array{str(ids)}) "
        "WITH ORDINALITY AS arr(elem, ord) ON d.id = arr.elem ORDER BY arr.ord"
    )
    query = select(models.Document).from_statement(raw)
    res = await session.execute(query)
    # q = session.query(models.Document).from_statement(raw)
    # res = await session.execute(q)
    return res.scalars().fetchall()
