import abc
import os
import pickle
import logging

import cachetools
import redis
from redis import asyncio as redis_async

_CACHE = None
logger = logging.getLogger(__name__)


def get_cache() -> 'Cache':
    global _CACHE
    if _CACHE is None:
        _CACHE = init_cache()
    return _CACHE


def init_cache():
    # default_ttl = 5 * 60  # 5 minutes
    default_ttl = 300  # 5 minutes
    redis_host = os.getenv('REDIS_HOST')
    if redis_host:
        logger.info(f'[CACHE] Init Redis cache with {redis_host}')
        cache = RedisCache(default_ttl)
    else:
        logger.info(f'[CACHE] Init local cache')
        cache = MemoryCache(default_ttl)
    return cache


class Cache:
    def __init__(self, ttl, maxsize):
        self.maxsize = maxsize
        self.ttl = ttl

    @abc.abstractmethod
    def set(self, key: str, value, ttl: int = None):
        pass

    @abc.abstractmethod
    async def aset(self, key: str, value, ttl: int = None):
        pass

    @abc.abstractmethod
    def get(self, key: str):
        pass

    @abc.abstractmethod
    async def aget(self, key: str):
        pass

    @abc.abstractmethod
    def get_prefix(self, key_prefix: str) -> list:
        pass

    @abc.abstractmethod
    def delete_prefix(self, key_prefix: str):
        pass

    @abc.abstractmethod
    async def adelete_prefix(self, key_prefix: str):
        pass

    @abc.abstractmethod
    def has_key(self, key: str) -> bool:
        pass

    @abc.abstractmethod
    def delete(self, key: str):
        pass

    @abc.abstractmethod
    async def adelete(self, key: str):
        pass

    @abc.abstractmethod
    def is_ok(self) -> bool:
        pass


class MemoryCache(Cache):
    def __init__(self, ttl: int, maxsize=10e5):
        super(MemoryCache, self).__init__(ttl, maxsize)
        self._inner = cachetools.TTLCache(self.maxsize, self.ttl)

    def set(self, key: str, value, ttl: int = None):
        self._inner[key] = value

    async def aset(self, key: str, value, ttl: int = None):
        self.set(key, value, ttl)

    def get(self, key: str):
        # logger.info("[CACHE] GET")
        return self._inner.get(key)

    async def aget(self, key: str):
        # logger.info("[CACHE] GET")
        return self.get(key)

    def get_prefix(self, key_prefix: str) -> list:
        vals = [v for key, v in self._inner.items() if key.startswith(key_prefix)]
        return vals

    def delete_prefix(self, key_prefix: str):
        keys = [key for key in self._inner if key.startswith(key_prefix)]
        for key in keys:
            # logger.info(f"[CACHE] DELETE {key}")
            self.delete(key)

    async def adelete_prefix(self, key_prefix: str):
        self.delete_prefix(key_prefix)

    def has_key(self, key: str) -> bool:
        return key in self._inner

    def delete(self, key: str):
        self._inner.pop(key, None)

    async def adelete(self, key: str):
        self.delete(key)

    def is_ok(self):
        return True


class RedisCache(Cache):
    def __init__(self, ttl: int, maxsize=10e5):
        redis_host = os.getenv('REDIS_HOST', 'localhost')
        redis_port = os.getenv('REDIS_SERVICE_PORT', 6379)
        self._redis = redis.StrictRedis(host=redis_host, port=redis_port, decode_responses=False)
        self._aredis = redis_async.StrictRedis(host=redis_host, port=redis_port, decode_responses=False)
        super(RedisCache, self).__init__(ttl, maxsize)

    def set(self, key: str, value, ttl=0):
        str_value = pickle.dumps(value)

        if ttl == 0:
            ttl = self.ttl
        self._redis.set(key, str_value, ex=ttl)

    async def aset(self, key: str, value, ttl: int = 0):
        str_value = pickle.dumps(value)

        if ttl == 0:
            ttl = self.ttl
        await self._aredis.set(key, str_value, ex=ttl)

    def get(self, key: str):
        str_value = self._redis.get(key)
        try:
            return pickle.loads(str_value)
        except:
            return None

    async def aget(self, key: str):
        str_value = await self._aredis.get(key)
        try:
            return pickle.loads(str_value)
        except:
            return None

    def get_prefix(self, key_prefix: str) -> list:
        keys = self._redis.keys(key_prefix+'*')
        res = []
        for key in keys:
            obj = self.get(key)
            res.append(obj)

        return res

    def delete_prefix(self, key_prefix: str):
        keys = self._redis.keys(key_prefix + '*')
        for key in keys:
            self.delete(key)

    async def adelete_prefix(self, key_prefix: str):
        keys = await self._aredis.keys(key_prefix + '*')
        for key in keys:
            await self.adelete(key)

    def has_key(self, key: str) -> bool:
        return self._redis.exists(key) == 1

    def delete(self, key: str):
        self._redis.delete(key)

    async def adelete(self, key: str):
        await self._aredis.delete(key)

    def is_ok(self):
        return self._redis.ping()
