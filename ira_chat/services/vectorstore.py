import logging
import os
from typing import Union

# import pinecone
import qdrant_client
from qdrant_client import models
# from langchain_community.vectorstores import Pinecone
from langchain_community.vectorstores import VectorStore
from langchain_core.documents import Document
from langchain_qdrant import QdrantVectorStore
from langchain_core.embeddings import Embeddings

from ira_chat.cache import function_cache
from ira_chat.services import llm as ira_llm
from ira_chat.services.qdrant_vs import QdrantVectorStoreEmbedMetadata

logger = logging.getLogger(__name__)


@function_cache.cached
def get_vectorstore(
    engine_type: str = None, org_config: dict | None = None, org_id=None, ws_id=None, override_config=None
) -> Union[VectorStore]:
    if not engine_type:
        from ira_chat.services import engine_manager
        engine_type = engine_manager.global_engine_type()

    return init_vectorstore(engine_type, org_config, org_id, ws_id, override_config)


def _default_vectorstore_config():
    vectorstore_type = os.environ.get('VECTORSTORE', 'qdrant')
    # if vectorstore_type == 'pinecone':
    #     return {
    #         'type': vectorstore_type,
    #         'pinecone_index': os.getenv('PINECONE_INDEX', 'hotline-dev'),
    #         'pinecone_api_key': os.getenv('PINECONE_API_KEY'),
    #         'pinecone_environment': os.getenv('PINECONE_ENVIRONMENT', 'us-west1-gcp-free'),
    #     }
    if vectorstore_type == 'qdrant':
        return {
            'type': vectorstore_type,
            'url': 'qdrant-headless' if os.getenv('KUBERNETES_SERVICE_PORT') else 'localhost',
            'port': 6333,
        }
    else:
        raise ValueError(f'Unrecognized vectorstore type "{vectorstore_type}"')


def get_index_namespace(suffix: str):
    prefix = os.getenv('ENV_PREFIX')
    return suffix if not prefix else f'{prefix}-{suffix}'


def get_indexed_contents_by_ids(vectorstore: VectorStore, ids: list[str], namespace: str = None) -> list[Document]:
    if isinstance(vectorstore, QdrantVectorStore):
        records = vectorstore.client.retrieve(collection_name=vectorstore.collection_name, ids=ids)
        return [
            (
                vectorstore._document_from_point(
                    result,
                    vectorstore.collection_name,
                    vectorstore.content_payload_key,
                    vectorstore.metadata_payload_key,
                )
            )
            for result in records
        ]
    else:
        raise NotImplementedError(f"Not implemented for the current vectorstore: {type(vectorstore)}")


@function_cache.cached
def get_embedding_model(org_config: dict, org_id: int, workspace_id: int, override_config: dict = None) -> Embeddings:
    llm_type = org_config.get('llm_type', 'openai')
    init_class = ira_llm.llms.get(llm_type)
    if init_class is None:
        raise TypeError(f'Unsupported llm_type {llm_type}')

    return init_class(org_config, {}, org_id, workspace_id).init_embeddings(override_config)


def get_vectorstore_params(org_config: dict | None = None):
    if 'vectorstore' in org_config and 'type' in org_config['vectorstore']:
        return org_config['vectorstore']
    else:
        return _default_vectorstore_config()


def init_vectorstore(
    engine_type: str, org_config: dict | None = None, org_id: int = None, workspace_id=None, override_config=None
):
    params = get_vectorstore_params(org_config)
    embed_model = get_embedding_model(org_config, org_id, workspace_id, override_config)
    vectorstore_type = params['type']
    # if vectorstore_type == 'pinecone':
    #     if engine_type == 'langchain':
    #     return init_pinecone_langchain(embed_model, params)
    #     else:
    #         llama
    #         return init_pinecone_llama(embed_model, params)
    if vectorstore_type == 'qdrant':
        return init_qdrant_langchain(embed_model, params)
    else:
        raise ValueError(f'Unrecognized vectorstore type "{vectorstore_type}"')


# def init_pinecone_langchain(embedding_model: Embeddings, pinecone_config: dict):
#     logger.info('[langchain] Initializing pinecone store...')
#     embeddings = embedding_model
#
#     index_name = pinecone_config['pinecone_index']
#     api_key = pinecone_config['pinecone_api_key']
#     env = pinecone_config['pinecone_environment']
#
#     # env format 'asia-southeast1-gcp'
#     pc = pinecone.Pinecone(api_key=api_key)
#
#     try:
#         index_desc = pc.describe_index(index_name)
#         index_host = index_desc.host
#     except pinecone.core.client.exceptions.NotFoundException:
#         # we create a new index
#         logger.info(f'Creating pinecone index {index_name}...')
#         cloud = env.split('-')[-1]
#         region = '-'.join(env.split('-')[:-1])
#         pc.create_index(
#             name=index_name,
#             metric='cosine',
#             dimension=1536,  # 1536 dim of text-embedding-ada-002,
#             spec=pinecone.ServerlessSpec(
#                 cloud=cloud,
#                 region=region
#             ),
#         )
#         logger.info(f'Index {index_name} created.')
#         index_desc = pc.describe_index(index_name)
#         index_host = index_desc.host
#
#     index = pc.Index(index_name, host=index_host)
#
#     langchain_pinecone_store = Pinecone(
#         index,
#         embeddings,
#         text_key='text',
#         # namespace=namespace,
#     )
#
#     logger.info('[langchain] Done initializing pinecone store.')
#     return langchain_pinecone_store


def init_qdrant_langchain(embedding_model: Embeddings, qdrant_config: dict):
    logger.info('[langchain] Initializing qdrant store...')
    embeddings = embedding_model

    url = qdrant_config.get('url', 'qdrant-headless' if os.getenv('KUBERNETES_SERVICE_PORT') else 'localhost')
    port = qdrant_config.get('port', 6333)
    grpc_port = qdrant_config.get('grpc_port', 6334)

    logger.info(f'[langchain] [Qdrant] Using {url}, port={port}, grpc_port={grpc_port}.')
    # q = qdrant_client.AsyncQdrantClient(
    q = qdrant_client.QdrantClient(
        url,
        port=port,
        grpc_port=grpc_port,
        timeout=60,
    )
    # Init special qdrant vectorstore with modified _generate_batches
    qdrant_store = QdrantVectorStoreEmbedMetadata(
        client=q, collection_name='default', embedding=embeddings, validate_collection_config=False
    )

    logger.info('[langchain] Done initializing qdrant store.')
    return qdrant_store


# def init_pinecone_llama(embedding_model: Embeddings, pinecone_config: dict):
#     logger.info('[llama] Initializing pinecone store...')
#     embeddings = embedding_model
#
#     index_name = pinecone_config['pinecone_index']
#     api_key = pinecone_config['pinecone_api_key']
#     env = pinecone_config['pinecone_environment']
#
#     pinecone.init(api_key=api_key, environment=env)
#
#     try:
#         index_desc = pinecone.describe_index(index_name)
#     except pinecone.core.client.exceptions.NotFoundException:
#         # we create a new index
#         logger.info(f'Creating pinecone index {index_name}...')
#         pinecone.create_index(
#             name=index_name,
#             metric='cosine',
#             dimension=1536  # 1536 dim of text-embedding-ada-002
#         )
#         logger.info(f'Index {index_name} created.')
#
#     index = pinecone.Index(index_name)
#
#     llama_pinecone_store = PineconeVectorStore(
#         index,
#         api_key=api_key,
#         text_key="text",
#         tokenizer=llama_pinecone.get_default_tokenizer(),
#     )
#
#     logger.info('Done initializing pinecone store.')
#     return llama_pinecone_store


# Metadata Filter Utilities for Unified Collections

def create_dataset_filter(dataset_id: int) -> models.Filter:
    return models.Filter(
        must=[
            models.FieldCondition(
                key="metadata.dataset_id",
                match=models.MatchValue(value=dataset_id)
            )
        ]
    )


def create_extractor_filter(extractor_id: int) -> models.Filter:
    """Create Qdrant filter for extractor-specific documents."""
    return models.Filter(
        must=[
            models.FieldCondition(
                key="metadata.extractor_id",
                match=models.MatchValue(value=extractor_id)
            )
        ]
    )


def create_timeline_filter(timeline_id: int) -> models.Filter:
    """Create Qdrant filter for timeline-specific documents."""
    return models.Filter(
        must=[
            models.FieldCondition(
                key="metadata.timeline_id",
                match=models.MatchValue(value=timeline_id)
            )
        ]
    )


def create_timeline_extractor_filter(timeline_id: int, extractor_id: int) -> models.Filter:
    """Create combined filter for specific timeline extractor."""
    return models.Filter(
        must=[
            models.FieldCondition(
                key="metadata.timeline_id",
                match=models.MatchValue(value=timeline_id)
            ),
            models.FieldCondition(
                key="metadata.extractor_id",
                match=models.MatchValue(value=extractor_id)
            )
        ]
    )


def create_workspace_filter(workspace_id: int) -> models.Filter:
    """Create Qdrant filter for workspace-specific documents."""
    return models.Filter(
        must=[
            models.FieldCondition(
                key="metadata.workspace_id",
                match=models.MatchValue(value=workspace_id)
            )
        ]
    )


def create_combined_filter(*filters: models.Filter) -> models.Filter:
    """Combine multiple filters into a single filter with AND logic."""
    all_conditions = []
    for filter_obj in filters:
        if filter_obj and filter_obj.must:
            all_conditions.extend(filter_obj.must)

    return models.Filter(must=all_conditions) if all_conditions else None


def create_subcollection_filter(subcollection_type: str, subcollection_id: str = None) -> models.Filter:
    """Create filter for specific subcollection type (extractor/timeline)."""
    conditions = [
        models.FieldCondition(
            key="metadata.subcollection_type",
            match=models.MatchValue(value=subcollection_type)
        )
    ]

    if subcollection_id:
        conditions.append(
            models.FieldCondition(
                key="metadata.subcollection_id",
                match=models.MatchValue(value=subcollection_id)
            )
        )

    return models.Filter(must=conditions)


async def delete_documents_with_filter(vectorstore: VectorStore, filter_obj: models.Filter) -> int:
    """
    Delete documents from vectorstore using metadata filter.

    Args:
        vectorstore: The vectorstore instance
        filter_obj: Qdrant filter to apply

    Returns:
        Number of documents deleted
    """
    if not isinstance(vectorstore, QdrantVectorStore):
        raise NotImplementedError(f"Filtered deletion not implemented for {type(vectorstore)}")

    try:
        # Use Qdrant's delete with filter functionality
        result = vectorstore.client.delete(
            collection_name=vectorstore.collection_name,
            points_selector=models.FilterSelector(filter=filter_obj)
        )

        # Log the deletion
        logger.info(f"Deleted documents from collection {vectorstore.collection_name} with filter: {filter_obj}")

        return result.operation_id if hasattr(result, 'operation_id') else 0

    except Exception as e:
        logger.error(f"Failed to delete documents with filter: {e}")
        raise


async def is_collection_empty(vectorstore: VectorStore) -> bool:
    """Check if a collection is empty (has no documents)."""
    if not isinstance(vectorstore, QdrantVectorStore):
        return False

    try:
        # Get collection info to check if it has any points
        collection_info = vectorstore.client.get_collection(vectorstore.collection_name)
        return collection_info.points_count == 0
    except Exception as e:
        logger.warning(f"Could not check if collection is empty: {e}")
        return False


async def safe_delete_collection_if_empty(vectorstore: VectorStore) -> bool:
    """
    Safely delete a collection if it's empty.

    Returns:
        True if collection was deleted, False otherwise
    """
    if not isinstance(vectorstore, QdrantVectorStore):
        return False

    try:
        if await is_collection_empty(vectorstore):
            vectorstore.client.delete_collection(vectorstore.collection_name)
            logger.info(f"Deleted empty collection: {vectorstore.collection_name}")
            return True
        else:
            logger.debug(f"Collection {vectorstore.collection_name} is not empty, keeping it")
            return False
    except Exception as e:
        logger.warning(f"Could not safely delete collection: {e}")
        return False
