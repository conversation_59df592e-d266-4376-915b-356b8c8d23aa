
import logging
from typing import Any, Callable, List

from langchain_core.vectorstores import VectorStore

from ira_chat.db import api as db_api
from ira_chat.db import models
from ira_chat.services import langchain_svc
from ira_chat.utils.utils import build_status


logger = logging.getLogger(__name__)


async def dataset_index_check(org_id, app: models.Workspace | models.Chat | models.Extractor):
    dataset_name, index_name = app.get_dataset_index()
    status = build_status(models.STATUS_SUCCESS)
    if dataset_name:
        try:
            dataset = await db_api.get_dataset_by_org_and_name(org_id, dataset_name)
        except Exception as e:
            msg = f"Dataset {dataset_name} not found"
            status = build_status(models.STATUS_ERROR, msg)
        else:
            if dataset.status['status'] == models.STATUS_ERROR:
                status = dataset.status
            elif index_name:
                try:
                    index = await db_api.get_dataset_index_by_dataset_and_name(dataset.id, index_name)
                except Exception as e:
                    msg = f"Index {index_name} not found"
                    status = build_status(models.STATUS_ERROR, msg)
                else:
                    if index.status['status'] == models.STATUS_ERROR:
                        status = index.status
    return await update_app_status(app, status)


async def update_app_status(app: models.Workspace | models.Chat | models.Extractor, status: dict):
    if app.status.get('status') == status['status'] and app.status.get('message') == status['message']:
        # Nothing changed, no need to update
        return app
    if app.status.get('status') != models.STATUS_ERROR and status['status'] == models.STATUS_SUCCESS:
        # if processing or something not error and new status is success, do not update
        return app

    if isinstance(app, models.Workspace):
        app = await db_api.update_workspace(app, {'status': status})
    elif isinstance(app, models.Chat):
        app = await db_api.update_chat(app, {'status': status})
    elif isinstance(app, models.Extractor):
        app = await db_api.update_extractor(app, {'status': status})

    return app


async def update_dataset_status(ds: models.Dataset, result: models.DatasetFile | Any):
    if isinstance(result, models.DatasetFile) and result.status['status'] == models.STATUS_ERROR:
        await db_api.update_dataset(
            ds, {'status': build_status(models.STATUS_ERROR, result.status['message'])}
        )
        logger.info(f"Change status to error [dataset={ds.name}, message={result.status['message']}]")
    else:
        files_in_processing = await db_api.get_dataset_file_count(
            dataset_id=ds.id, statuses=[models.STATUS_PROCESSING, models.STATUS_PROCESSING_META, models.STATUS_PROCESSING_CHUNK_META]
        )
        files_in_error = await db_api.list_dataset_files(ds.id, status=models.STATUS_ERROR)
        index_in_processing = await db_api.get_dataset_index_count(
            dataset_id=ds.id, status=models.STATUS_PROCESSING
        )
        changed_dataset = await db_api.get_dataset(ds.id)
        if len(files_in_error) > 0:
            if changed_dataset.status['status'] != models.STATUS_ERROR:
                msg = files_in_error[0].status['message']

                logger.info(f"Change status to error [dataset={ds.name}, message={msg}]")

                await db_api.update_dataset(ds, {'status': build_status(models.STATUS_ERROR, msg)})

        elif files_in_processing == 0 and len(files_in_error) == 0 and index_in_processing == 0 and \
                changed_dataset.status['status'] != models.STATUS_ERROR:
            logger.info(f"Change status to SUCCESS [dataset={ds.name}, message=None]")
            await db_api.update_dataset(ds, {'status': build_status(models.STATUS_SUCCESS)})


def watch_dataset(update_stats=False):
    def watch_dataset_decorator(func: Callable[..., models.DatasetFile | Any]):
        async def wrapper(self, *args, **kwargs):
            if hasattr(self, 'ds'):
                ds = self.ds
            else:
                ds = self._dataset

            if update_stats:
                await db_api.update_dataset(ds, {
                    'total_size': await db_api.get_dataset_file_size_sum(ds.id),
                    'file_count': await db_api.get_dataset_file_count(dataset_id=ds.id),
                })
            logger.info(f"Change status to PROCESSING [dataset={ds.name}, message=None]")
            await db_api.update_dataset(ds, {'status': build_status(models.STATUS_PROCESSING)})
            result = await func(self, *args, **kwargs)

            await update_dataset_status(ds, result)

            return result
        return wrapper

    return watch_dataset_decorator


def watch_index(update_stats=False):
    def watch_index_decorator(func):
        async def wrapper(self, *args, **kwargs):
            if hasattr(self, '_index'):
                index = self._index
            else:
                index = [arg for arg in args if isinstance(arg, models.DatasetIndex)][0]
            logger.info(f"Change status to PROCESSING [index={index.name}, message=None]")
            await db_api.update_dataset_index(index, {'status': build_status(models.STATUS_PROCESSING)})
            result = await func(self, *args, **kwargs)

            changed_index = await db_api.get_dataset_index(index.id)
            if changed_index.status['status'] != models.STATUS_ERROR:
                logger.info(f"Change status to SUCCESS [index={index.name}, message=None]")
                await db_api.update_dataset_index(index, {'status': build_status(models.STATUS_SUCCESS)})

            if update_stats:
                if hasattr(self, 'vectorstore'):
                    await update_index_stats(index, [self.vectorstore, self.vectorstore_answers])
                if hasattr(self, '_get_index_vectorstore'):
                    await update_index_stats(index, self._get_index_vectorstore(index))

            return result
        return wrapper

    return watch_index_decorator


async def update_index_stats(index: models.DatasetIndex, vss: List[VectorStore | langchain_svc.NoneVectorStore]):
    size, vector_count = 0, 0
    for vs in vss:
        if not vs:
            continue
        if vs is langchain_svc.NoneVectorStore:
            continue
        size_tmp, vector_count_tmp = langchain_svc.get_vectorstore_size_vector_count(vs)
        size += size_tmp
        vector_count += vector_count_tmp
    update_data = {
        'total_size': size,
        'vector_count': vector_count,
        'file_count': await db_api.get_dataset_index_file_count(index.id),
    }
    await db_api.update_dataset_index(index, update_data)


def get_dataset_index_names(config: dict):
    return models.get_dataset_index(config)
