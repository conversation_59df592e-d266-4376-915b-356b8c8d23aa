from typing import Iterator, List, Optional, Sequence, Tuple

from langchain_core.documents import Document
from langchain_core.stores import BaseStore

from ira_chat.db import api as db_api


class PostgresDatasetsDocStore(BaseStore[str, Document]):
    """BaseStore implementation using Postgres as the underlying store.
    """

    def __init__(self) -> None:
        pass

    async def amget(self, keys: Sequence[str]) -> List[Optional[Document]]:
        result = await db_api.list_dataset_file_documents_by_ids_async_order(ids=keys)
        docs = [Document(d.content, metadata=d.get_doc_metadata()) for d in result]
        return docs

    def mget(self, keys: Sequence[str]) -> List[Optional[Document]]:
        """Get the list of documents associated with the given keys.

        Args:
            keys (list[str]): A list of keys representing Document IDs.

        Returns:
            list[Document]: A list of Documents corresponding to the provided
                keys, where each Document is either retrieved successfully or
                represented as None if not found.
        """
        result = db_api.list_dataset_file_documents_by_ids_sync_order(ids=keys)
        docs = [Document(d.content, metadata=d.get_doc_metadata()) for d in result]
        return docs

    def mset(self, key_value_pairs: Sequence[Tuple[str, Document]]) -> None:
        """Set the given key-value pairs.

        Args:
            key_value_pairs (list[tuple[str, Document]]): A list of id-document
                pairs.
        Returns:
            None
        """
        raise NotImplementedError()

    def mdelete(self, keys: Sequence[str]) -> None:
        """Delete the given ids.

        Args:
            keys (list[str]): A list of keys representing Document IDs.
        """
        raise NotImplementedError()

    def yield_keys(self, prefix: Optional[str] = None) -> Iterator[str]:
        """Yield keys in the store.

        Args:
            prefix (str): prefix of keys to retrieve.
        """
        raise NotImplementedError()
