import logging

import cv2
import numpy as np
import pytesseract
import imutils

logger = logging.getLogger(__name__)


def document_norm_rotate(image_rgb=None, image_data=None, filename=None) -> tuple[np.ndarray, int]:
    if not (image_data or filename or image_rgb is not None):
        raise ValueError('Must provide either image_rgb or filename or image_data')

    # load the input image, convert it from BGR to RGB channel ordering,
    # and use Tesseract to determine the text orientation
    if filename:
        image_bgr = cv2.imread(filename)
        image_rgb = cv2.cvtColor(image_bgr, cv2.COLOR_BGR2RGB)
        del image_bgr
    elif image_data:
        image_bgr = cv2.imdecode(np.frombuffer(image_data, np.uint8), cv2.IMREAD_COLOR)
        image_rgb = cv2.cvtColor(image_bgr, cv2.COLOR_BGR2RGB)
        del image_bgr

    if len(image_rgb.shape) == 2:
        # 2D gray/black/white image
        image_rgb = np.expand_dims(image_rgb, 2)
        if image_rgb.dtype == np.bool_:
            # True black/white
            image_gray = cv2.cvtColor(image_rgb.astype(np.uint8) * 255, cv2.COLOR_GRAY2RGB)
            image_rgb = image_gray
        else:
            # Grayscale?
            image_gray = cv2.cvtColor(image_rgb, cv2.COLOR_GRAY2RGB)
            image_rgb = image_gray
    else:
        if image_rgb.shape[2] == 4:
            image_gray = cv2.cvtColor(image_rgb, cv2.COLOR_RGBA2GRAY)
        else:
            image_gray = cv2.cvtColor(image_rgb, cv2.COLOR_RGB2GRAY)

    try:
        results = pytesseract.image_to_osd(image_gray, output_type=pytesseract.Output.DICT)
    except pytesseract.pytesseract.TesseractError as e:
        if 'Too few characters' in str(e):
            logger.info("[Auto-rotate] Too few characters on the page; SKIP")
            return image_rgb, 0
        else:
            raise
    finally:
        del image_gray

    logger.info("[Auto-rotate] Detected orientation: {}".format(results["orientation"]))

    rotate = results['rotate']
    if rotate == 0 or (160 < rotate < 200):
        if 160 < rotate < 200:
            logger.info("[Auto-rotate] Problematic orientation: {}; SKIP".format(results["orientation"]))
        return image_rgb, rotate

    logger.info("[Auto-rotate] Rotate by {} degrees to correct".format(rotate))
    # Drop alpha channel if any
    if len(image_rgb.shape) == 3 and image_rgb.shape[2] == 4:
        image_rgb = cv2.cvtColor(image_rgb, cv2.COLOR_RGBA2RGB)

    rotated = imutils.rotate_bound(image_rgb, angle=results["rotate"])
    del image_rgb
    return rotated, rotate


def increase_contrast(img):
    lab = cv2.cvtColor(img, cv2.COLOR_BGR2LAB)
    l_channel, a, b = cv2.split(lab)

    # Applying CLAHE to L-channel
    # feel free to try different values for the limit and grid size:
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
    cl = clahe.apply(l_channel)

    # merge the CLAHE enhanced L-channel with the a and b channel
    limg = cv2.merge((cl, a, b))

    # Converting image from LAB Color model to BGR color spcae
    enhanced_img = cv2.cvtColor(limg, cv2.COLOR_LAB2BGR)
    return enhanced_img
