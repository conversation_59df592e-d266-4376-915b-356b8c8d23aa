import asyncio
import datetime
import logging
from typing import AsyncIterator

from langchain_core.documents import Document
from langchain_core.language_models import BaseChatModel

from ira_chat import exceptions
from ira_chat.db import api as db_api, models, base
from ira_chat.services import engine_manager, jobs
from ira_chat.services import langchain_svc, suggestions
from ira_chat.services import llm as ira_llm
from ira_chat.services import ragas_metrics_tasks
from ira_chat.services.langchain_svc import get_default_prompts
from ira_chat.services.llms.base import InitLLM, get_trace_callbacks
from ira_chat.utils import llm_utils, utils, metric_utils

logger = logging.getLogger(__name__)
AGENTIC_RAG = 'agentic_rag'
SIMPLE_RAG = 'simple_rag'
SUBQUERY_RAG = 'subquery_rag'
NO_ANSWER_REASONS_IGNORE = {'not_chat_topics'}
DEFAULT_HISTORY_LIMIT = 10
METRIC_WEIGHTS = {
    models.METRIC_FAITHFULNESS: 1.0,
    models.METRIC_CONTEXT_UTIL: 1.0,
    models.METRIC_RELEVANCE: 1.0,
}
_exclude_answer_keys = [
    'generated_question',
    'question',
    'source_documents',
    'messages',
    'answer',
    'initial_documents',
    'org_id',
    'workspace_id',
    'dataset_id',
]


class ChatEngineManager(engine_manager.EngineManager):
    async def get_chat_engine(self, config=None, more_params=None):
        config = self._override_config(config)

        config['enable_tracing'] = True

        init_llm, params = self.get_init_llm(config=config)
        # TODO Use get_embedding_model to make use of cache
        embeddings = init_llm.init_embeddings()
        if more_params:
            params.update(more_params)
        llm = init_llm.init_llm(params)
        streaming_llm = None
        if params.get('stream'):
            streaming_llm = init_llm.init_streaming_llm(params)
        if not params.get('language'):
            params['language'] = config.get('language')

        # params['init_topics_vectorstore'] = lambda: self.topics_vectorstore
        params['init_not_topics_vectorstore'] = lambda: self.not_topics_vectorstore
        # Corrected answers retriever
        params['answers_retriever'] = await self._get_retriever(
            search_k=config.get('answers_search_k') or 8, vectorstore=self.vectorstore_answers
        )
        # General retriever
        retriever = await self._get_retriever()

        prompt_params = ira_llm.get_prompt_params(get_default_prompts(config), config)
        chat_type = config.get('chat_type')

        if chat_type == AGENTIC_RAG:
            chain = langchain_svc.get_agent_chain(llm, streaming_llm, retriever, prompt_params, params)
        elif chat_type == SUBQUERY_RAG:
            # Enable subquery agent for this chat type
            params['use_subquery_agent'] = True
            chain = langchain_svc.get_agent_chain(llm, streaming_llm, retriever, prompt_params, params)
        else:
            chain = langchain_svc.get_chain(llm, streaming_llm, retriever, prompt_params, params)

        return chain, init_llm, llm, embeddings, config

    @jobs.job_tracker
    async def index_chat_topics(self):
        await asyncio.sleep(engine_manager.SMALL_SLEEP)
        config = self.app_config
        chat_topics = config.get('chat_topics', None)
        not_chat_topics = config.get('not_chat_topics', None)
        loop = asyncio.get_event_loop()

        if not chat_topics and not not_chat_topics:
            return

        logger.info("[Chat topics] Trigger indexing chat topics")

        if chat_topics:
            topics_vs = self.topics_vectorstore
            # TODO make this more effective (create embedding id records for each topic etc.)
            loop.run_in_executor(None, langchain_svc.clear_vectorstore, topics_vs)
            ids = [utils.generate_unicode_uuid() for _ in chat_topics]
            docs = [Document(q, metadata={}) for q in chat_topics]
            await topics_vs.aadd_documents(docs, ids=ids)

        if not_chat_topics:
            not_topics_vs = self.not_topics_vectorstore
            # TODO make this more effective (create embedding id records for each topic etc.)
            loop.run_in_executor(None, langchain_svc.clear_vectorstore, not_topics_vs)
            ids = [utils.generate_unicode_uuid() for _ in not_chat_topics]
            docs = [Document(q, metadata={}) for q in not_chat_topics]
            await not_topics_vs.aadd_documents(docs, ids=ids)

        logger.info("[Chat topics] Done indexing chat topics")

    def get_index_way(self):
        return 'files' if not self._index else 'datasets'

    def get_working_dataset(self):
        return self._dataset

    async def chat(self, engine, message: models.ChatMessage, history: list[models.ChatMessage] = None):
        await self._generate_fake_error(message.content)

        tuple_history = [(m.role, m.content) for m in history if m.content]
        chat_type = self.app_config.get('chat_type')

        if chat_type in [AGENTIC_RAG, SUBQUERY_RAG]:
            tuple_history += [('user', message.content)]
            inputs = {
                "messages": tuple_history,
                'question': message.content,
                'generated_question': message.content,
                'source_documents': [],
                'org_id': self.org.id,
                'workspace_id': self.ws.id,
                'dataset_id': self.get_working_dataset().id,
            }
        else:
            inputs = {
                "chat_history": tuple_history,
                'question': message.content,
                'generated_question': message.content,
                'source_documents': [],
            }
        response = await engine.ainvoke(inputs, config={"callbacks": get_trace_callbacks()})
        """
        {"source_documents": [
          {"page_content": "", "metadata": {"page": 19.0, "source": "SEGUR.pdf"}}, "score": 0.8
         ],
         "answer": "xxx",
         "question": "yyy"
        }
        """
        intermediate_response = {
            'generated_question': response['generated_question'],
            'question': message.content,
            'source_documents': [d.model_dump(exclude_unset=True) for d in response['source_documents']],
        }
        for k, v in response.items():
            if k not in intermediate_response:
                intermediate_response[k] = v
        return intermediate_response

    @jobs.job_tracker
    async def handle_message(
        self,
        chat: models.Chat,
        input_message: models.ChatMessage,
        output_message: models.ChatMessage
    ):
        # Let the main thread go
        await asyncio.sleep(engine_manager.SMALL_SLEEP)

        await db_api.update_metrics_for_chat(chat)
        # noinspection PyBroadException
        try:
            metric_config = {
                'app_type': models.APP_TYPE_CHAT,
                'app_id': chat.id,
                'object_id': output_message.id,
            }
            chat_engine, init_llm, llm, embeddings, resolved_config = await self.get_chat_engine(metric_config)

            history = await self._get_chat_history(chat.id, input_message.id, output_message.id)
            optimized_history = self._optimize_chat_history(history, init_llm, llm)

            logger.info(f'Generating answer in chat [id={chat.id}, question={input_message.content}]')
            response = await self.chat(chat_engine, input_message, optimized_history)
        except Exception as e:
            await self._process_message_error(chat, input_message, output_message, e)
            return

        await self._postprocess_message(
            response, chat, input_message, output_message, init_llm, llm, embeddings, optimized_history, resolved_config
        )

    @jobs.job_tracker
    async def handle_message_stream(
        self, chat: models.Chat,
        input_message: models.ChatMessage,
        output_message: models.ChatMessageOutput,
    ) -> AsyncIterator[dict] | models.ChatMessageOutput:
        # Let the main thread go
        await asyncio.sleep(engine_manager.SMALL_SLEEP)

        await db_api.update_metrics_for_chat(chat)
        stream_queue = asyncio.Queue()
        stream_done = asyncio.Event()

        # noinspection PyBroadException
        try:
            metric_config = {
                'app_type': models.APP_TYPE_CHAT,
                'app_id': chat.id,
                'object_id': output_message.id,
            }
            stream_params = {
                'stream': True,
                'stream_queue': stream_queue,
                'stream_event': stream_done
            }
            chat_engine, init_llm, llm, embeddings, resolved_config = await self.get_chat_engine(
                metric_config, stream_params
            )

            history = await self._get_chat_history(chat.id, input_message.id, output_message.id)
            optimized_history = self._optimize_chat_history(history, init_llm, llm)

            logger.info(f'Generating answer in chat [id={chat.id}, question={input_message.content}]')
            response_coro = self.chat(chat_engine, input_message, optimized_history)
            # asyncio.create_task(response_coro)
            t = asyncio.create_task(self._postprocess_message(
                response_coro, chat, input_message, output_message,
                init_llm, llm, embeddings, optimized_history, resolved_config, done=stream_done
            ))
        except Exception as e:
            return await self._process_message_error(chat, input_message, output_message, e)

        return self._get_stream_iterator(stream_queue, stream_done)

    async def extract_answer_source(self, response):
        # query = response['question']
        # answer = response['answer']
        answer_source = [doc for doc in response['source_documents']]
        # embeddings = await self.get_embedding_model().aembed_documents([page['page_content'] for page in answer_source])

        raw_context_list = [page['page_content'] for page in answer_source]
        # query_answer_embedding = await self.get_embedding_model().aembed_query(query + '\n' + answer)
        # distances = distances_from_embeddings(query_answer_embedding, embeddings)
        for i, src in enumerate(answer_source):
            meta = answer_source[i].get('metadata', {})
            answer_source[i]['score'] = meta.get('rrf_score') or meta.get('score') or 0.0

        # answer_source = sorted(answer_source, key=lambda x: x['score'], reverse=True)
        # max_score = str(answer_source[0]["score"]) if len(answer_source) > 0 else 'N/A'
        # answer_source = [a for a in answer_source if a['score'] > 0.735]
        return answer_source, raw_context_list

    @staticmethod
    async def _generate_fake_error(message):
        if message.startswith("ERROR_RESPONSE"):
            if message.startswith("ERROR_RESPONSE_DELAY"):
                # noinspection PyBroadException
                try:
                    delay = float(message[len("ERROR_RESPONSE_DELAY"):].strip())
                except Exception:
                    delay = 1
                await asyncio.sleep(delay)
                raise exceptions.FakeError(f"Fake error after {delay} seconds delay")
            raise exceptions.FakeError("Fake error")
        else:
            return

    @staticmethod
    async def _get_stream_iterator(queue: asyncio.Queue, done: asyncio.Event) -> AsyncIterator[dict]:
        while not queue.empty() or not done.is_set():
            # Wait for the next token in the queue,
            # but stop waiting if the done event is set
            future_done, other = await asyncio.wait(
                [
                    # NOTE: If you add other tasks here, update the code below,
                    # which assumes each set has exactly one task each
                    asyncio.ensure_future(queue.get()),
                    asyncio.ensure_future(done.wait()),
                ],
                return_when=asyncio.FIRST_COMPLETED,
            )

            # Cancel the other task
            if other:
                other.pop().cancel()

            # Extract the value of the first completed task
            chunk_or_done = future_done.pop().result()

            # If the extracted value is the boolean True, the done event was set
            if chunk_or_done is True:
                break

            # Otherwise, the extracted value is a token, which we yield
            yield chunk_or_done

    async def _process_message_error(
        self, chat: models.Chat, input_message, output_message: models.ChatMessageOutput, exc
    ) -> models.ChatMessageOutput:
        msg = f'{exc.__class__.__name__}: {str(exc)}'
        new_message = {
            'content': msg,
            'role': models.ROLE_SERVICE,
            'status': models.STATUS_ERROR,
        }

        await db_api.update_message(input_message, {'status': models.STATUS_ERROR_REASON})
        await db_api.update_message_output(output_message, new_message)
        logger.exception(msg)

        _ = asyncio.create_task(self.send_all_webhooks(
            {
                'chat_id': input_message.chat_id,
                'message_id': input_message.id,
                'result_id': output_message.id,
                'workspace_id': self.ws.id,
                'status': new_message['status'],
            },
            self.ws
        ))
        await db_api.update_metrics_for_chat(chat)
        # Create metric error
        await db_api.create_metric({
            'org_id': self.org.id,
            'workspace_id': self.ws.id,
            'app_type': models.APP_TYPE_CHAT,
            'app_id': chat.id,
            'object_id': output_message.id,
            'type': models.METRIC_ERROR,
            'value': 1,
        })

        return output_message

    async def _get_chat_history(self, chat_id, input_message_id, output_message_id):
        history_limit = int(self.app_config.get('chat_history_limit') or DEFAULT_HISTORY_LIMIT)
        messages = await db_api.list_messages(
            chat_id=chat_id,
            end_id=input_message_id - 1,
            ignore_statuses=[models.STATUS_ERROR, models.STATUS_ERROR_REASON],
            limit=history_limit,
        )
        results = await db_api.list_last_message_outputs(
            chat_id=chat_id,
            ignore_statuses=[models.STATUS_ERROR, models.STATUS_ERROR_REASON],
            end_id=output_message_id - 1,
            limit=history_limit,
        )
        # Build a flat list
        history = sorted(messages + results, key=lambda x: x.created_at)
        return history

    @staticmethod
    def _optimize_chat_history(
        history: list[models.ChatMessage | models.ChatMessageOutput],
        init_llm: InitLLM,
        llm: BaseChatModel,
    ):
        # init_llm: InitLLM = llm.metadata['init_llm']
        context_limit, output_limit = init_llm.token_limits(
            model_name=llm_utils.get_model_name(llm)
        )
        optimized_history = ira_llm.optimize_messages_to_limit(
            history, limit=int(context_limit * 0.9), model_name=init_llm.model_name
        )
        return optimized_history

    @jobs.job_tracker
    async def _postprocess_message(
        self,
        response_or_coro,
        chat: models.Chat,
        input_message: models.ChatMessage,
        output_message: models.ChatMessageOutput,
        init_llm, llm, embeddings,
        messages: list[models.ChatMessage | models.ChatMessageOutput],
        full_config: dict = None,
        done: asyncio.Event = None,
    ):
        if asyncio.iscoroutine(response_or_coro):
            try:
                response = await response_or_coro
            except Exception as e:
                if done is not None:
                    # Close iter loop
                    done.set()
                await self._process_message_error(chat, input_message, output_message, e)
                return
        else:
            response = response_or_coro

        logger.info(f'Got answer from LLM for chat [id={chat.id}]')

        answer = response['answer']
        not_an_answer = response.get('not_answer')
        not_answer_reason = response.get('not_answer_reason')
        cache_hit = bool(response.get('cache_hit'))
        source_documents, raw_context_list = await self.extract_answer_source(response)

        update_result = {
            'content': answer,
            'context': {'source_documents': source_documents} | {
                k: v for k, v in response.items() if k not in _exclude_answer_keys
            },
            'role': models.ROLE_AI,
            'status': models.STATUS_GENERATING_QUESTION,
        }

        async with base.session_context():
            await db_api.update_message(input_message, {'status': models.STATUS_SUCCESS})
            # Create real message response metric
            await self.create_metric(
                self.org.id,
                chat.workspace_id,
                chat.id,
                models.METRIC_MESSAGE_RESPONSE_TIME,
                output_message.id,
                models.APP_TYPE_CHAT,
                value=(models.now() - output_message.created_at).total_seconds(),
            )
            output_message = await db_api.update_message_output(output_message, update_result)

        logger.info(
            f'Saved answer in chat [id={chat.id}, answer={answer}, '
            f'result_id={output_message.id}]'
        )
        util_llm = llm_utils.set_cheapest_model(llm, custom_model=init_llm.llm_params.get('utility_model'))

        # Send webhooks once answer is generated
        _ = asyncio.create_task(self.send_all_webhooks(
            {
                'chat_id': chat.id,
                'message_id': input_message.id,
                'result_id': output_message.id,
                'workspace_id': self.ws.id,
                'status': models.STATUS_GENERATING_QUESTION,
            },
            self.ws
        ))
        # Handle suggestions
        new_message_with_status: models.ChatMessageOutput = await suggestions.generate_suggestions(
            init_llm, util_llm, messages, input_message, output_message, source_documents,
            language=self.app_config.get('language', 'english')
        )

        # Send webhooks on SUCCESS
        _ = asyncio.create_task(self.send_all_webhooks(
            {
                'chat_id': chat.id,
                'message_id': input_message.id,
                'result_id': output_message.id,
                'workspace_id': self.ws.id,
                'status': new_message_with_status.status,
            },
            self.ws
        ))

        # Handle metrics
        no_answer_excluded = not_an_answer and not_answer_reason in NO_ANSWER_REASONS_IGNORE
        no_answer_completely = not_an_answer and not_answer_reason not in NO_ANSWER_REASONS_IGNORE
        if no_answer_excluded or no_answer_completely:
            grade_selector = models.GradeSelector.NO_ANSWER_EXCLUDED if no_answer_excluded else models.GradeSelector.NO_ANSWER_COMPLETELY
            await metric_utils.ensure_grade_metric(self.org.id, self.ws.id, chat.id, output_message.id, grade_selector)
            # await metric_utils.ensure_rating_metric(self.ws.id, chat.id, output_message.id, metric_utils.grade_to_rating(grade_selector))
            await db_api.update_message_output(
                # output_message, {'grade_selector': grade_selector, 'rating': metric_utils.grade_to_rating(grade_selector)}
                output_message, {'grade_selector': grade_selector}
            )

        if not not_an_answer:
            # insert_all = 1 if no_answer_condition else 0 if no_answer_completely else None
            insert_all = 1 if no_answer_excluded else None
            should_evaluate = not no_answer_completely
            if should_evaluate:
                await cache_processing(
                    input_message, output_message,
                    self.get_working_dataset().id,
                    self.org.id,
                    self.ws.id,
                    cache_hit, config=self.app_config,
                )

                # try:
                engine_params, config = self.get_initialization_params(full_config)
                await ragas_metrics_tasks.run_evaluation_task(
                    engine_params, config, models.APP_TYPE_CHAT, chat.id, output_message.id,
                    input_message.content, answer, raw_context_list,
                    insert_all=insert_all
                )
                    # metric_dicts_with_average = append_average_metric(metric_dicts)
                    # await db_api.create_metrics(metric_dicts_with_average)
                # except Exception as e:
                #     logger.error(f'Error running evaluation: {e}')

        # await metric_utils.squash_metrics(self.org.id, self.ws.id, chat.id, models.APP_TYPE_CHAT, output_message.id)

        return output_message


async def cache_processing(
    input_message: models.ChatMessage,
    output_message: models.ChatMessageOutput,
    dataset_id: str,
    org_id: int,
    workspace_id: int,
    cache_hit: bool,
    config: dict,
):
    cache_enabled = config.get('cache_enabled', False)
    cache_ttl_days = int(config.get('cache_ttl_days') or 30)
    if not cache_enabled:
        return
    if cache_hit:
        await db_api.create_metric({
            'org_id': org_id,
            'workspace_id': workspace_id,
            'app_type': models.APP_TYPE_CHAT,
            'app_id': output_message.chat_id,
            'object_id': output_message.id,
            'type': models.METRIC_CACHE_HIT,
            'value': 1,
        })
    else:
        # await db_api.create_metric({
        #     'org_id': org_id,
        #     'workspace_id': output_message.workspace_id,
        #     'app_type': models.APP_TYPE_CHAT,
        #     'app_id': output_message.chat_id,
        #     'object_id': output_message.id,
        #     'type': models.METRIC_CACHE_MISS,
        #     'value': 1,
        # })
        await db_api.create_message_cached({
            'dataset_id': dataset_id,
            'org_id': org_id,
            'content': input_message.content,
            'output': output_message.content,
            'context': output_message.context,
            'hash': utils.hash_sha256(input_message.content),
            'expires_at': base.now() + datetime.timedelta(days=cache_ttl_days),
        })
