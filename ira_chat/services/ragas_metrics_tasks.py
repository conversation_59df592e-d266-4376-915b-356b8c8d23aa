import logging
from typing import List

from ira_chat.db import api as db_api, models, base
from ira_chat.config import broker
from ira_chat.services import ragas_metrics
from ira_chat.utils import llm_utils, metric_utils

logger = logging.getLogger(__name__)


async def run_evaluation_task(
    engine_params: dict, config: dict, app_type: str, app_id: int, object_id: int,
    input: str, output: str, contexts: List[str], insert_all: float = None,
):
    return await run_evaluation_taskiq.kiq(
        engine_params, config, app_type, app_id, object_id, input, output, contexts, insert_all
    )


@broker.get_broker().task()
async def run_evaluation_taskiq(
    engine_params: dict, config: dict, app_type: str, app_id: int, object_id: int,
    input: str, output: str, contexts: List[str], insert_all: float = None,
):
    # To avoid circular import
    from ira_chat.services.engine_manager_chats import ChatEngineManager

    org = models.Organization(**engine_params['org'])
    ws = models.Workspace(**engine_params['ws'])
    manager = await ChatEngineManager.init_async(
        org, ws, app=engine_params['app'], app_type=engine_params['app_type'],
    )

    init_llm, llm_params = manager.get_init_llm(config)
    llm = init_llm.init_llm(llm_params)
    util_llm = llm_utils.set_cheapest_model(llm, custom_model=init_llm.llm_params.get('utility_model'))
    embeddings = init_llm.init_embeddings()

    metric_dicts = await ragas_metrics.run_evaluation(
        org.id, ws.id, app_type, app_id, object_id,
        util_llm, embeddings, input, output, contexts, insert_all
    )
    async with base.session_context():
        metric_dicts_with_average = ragas_metrics.append_average_metric(metric_dicts)
        await db_api.create_metrics(metric_dicts_with_average)

        await metric_utils.squash_metrics(manager.org.id, manager.ws.id, app_id, app_type, object_id)

    # del everything
    del llm
    del init_llm
    del llm_params
    del manager
    del org
    del ws
    del util_llm
    del embeddings
    del metric_dicts
    del metric_dicts_with_average
