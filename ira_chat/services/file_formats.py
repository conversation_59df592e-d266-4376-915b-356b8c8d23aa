import logging
import os
import re
import tempfile
import zipfile
from typing import Any

import langchain.schema
import pymupdf
import tiktoken
from PyPDF2 import PdfReader
from fastapi import HTTPException
from langchain.text_splitter import TokenTextSplitter
from langchain_community.document_loaders import Docx2txtLoader, CSVLoader
from langchain_community.document_loaders import PyMuPDFLoader
from langchain_community.document_loaders import TextLoader
from langchain_community.document_loaders import UnstructuredExcelLoader
from langchain_community.document_loaders import UnstructuredHTMLLoader
from langchain_community.document_loaders import UnstructuredXMLLoader
from langchain_core.documents import Document
from langchain_core.embeddings import Embeddings
from langchain_experimental.text_splitter import SemanticChunker
from langchain_text_splitters import RecursiveCharacterTextSplitter, Language

from ira_chat import exceptions
from ira_chat.config.shared_config import SharedConfig
from ira_chat.db import models
from ira_chat.services import chunker

logger = logging.getLogger(__name__)
supported_formats = ['.pdf', 'txt', '.md', '.docx', '.doc', '.html', '.xls', '.xlsx', '.xml']


# TODO: refactor get splitter and usages?
def get_text_splitter(embs: Embeddings = None, **kwargs):
    if embs:
        sem_chunker = SemanticChunker(
            embeddings=embs,
            breakpoint_threshold_type='gradient',
            min_chunk_size=500,
            add_start_index=True,
        )
        return sem_chunker
    else:
        # Check if the file is markdown
        file_ext = kwargs.get('file_ext', '')
        if file_ext.lower() == '.md':
            tokenizer = kwargs.get('tokenizer') or tiktoken.encoding_for_model("gpt-4o")
            return CustomMarkdownTextSplitter(
                chunk_size=kwargs.get('chunk_size') or 600,
                chunk_overlap=kwargs.get('chunk_overlap') or 60,
                length_function=lambda text: len(tokenizer.encode(text)),
                # Match **3. FORME PHARMACEUTIQUE**
                # Match **4.1.** **Indications thérapeutiques**
                # Match **4.2.** **Posologie et mode d'administration**
                # Match **4. Délivrance**
                more_separators=[r'\n\*\*[0-9\.?]+\**\s*\**[\w\s\']+\*\*'],
                is_separator_regex=True,
                # separators=[re.compile(r'\*\*[0-9\.?]+\**\s*\**[\w\s\']+\*\*')],
            )
            # return MarkdownTokenTextSplitter(
            #     model_name="gpt-4o",
            #     chunk_size=kwargs.get('chunk_size') or 1200,
            #     chunk_overlap=kwargs.get('chunk_overlap') or 100
            # )
        else:
            return TokenTextSplitter(
                model_name="gpt-4o",
                # encoding_name="cl100k_base",
                chunk_size=kwargs.get('chunk_size') or 1200,
                chunk_overlap=kwargs.get('chunk_overlap') or 100
            )


class CustomMarkdownTextSplitter(RecursiveCharacterTextSplitter):
    """Attempts to split the text along Markdown-formatted headings."""

    def __init__(self, more_separators: list[str] | None = None, **kwargs: Any) -> None:
        """Initialize a MarkdownTextSplitter."""
        separators = self.get_separators_for_language(Language.MARKDOWN)
        if more_separators:
            separators = more_separators + separators
        super().__init__(separators=separators, **kwargs)


def get_child_splitter():
    # return RecursiveCharacterTextSplitter(
    return TokenTextSplitter(
        model_name="gpt-4o",
        chunk_size=100,
        chunk_overlap=10
    )


def get_corrected_answer_chunker():
    return chunker.CorrectedAnswerChunker(keyword='Human: ')


def load_file(data, filename: str, additional_meta=None) -> list[langchain.schema.Document]:
    _, ext = os.path.splitext(filename)
    ext = ext.lower()
    if isinstance(data, str):
        data = data.encode()

    tmp = save_to_tmp_file(data, ext)
    if ext == '.pdf':
        # docs = PyMuPDFLoader(tmp, extract_images=True, images_parser=RapidOCRBlobParser()).load()
        # docs = PyMuPDFLoader(tmp).load()
        # Check if the PDF contains password
        doc = pymupdf.open(filename=tmp, filetype='pdf')
        if doc.is_encrypted or doc.needs_pass:
            raise exceptions.PasswordError('PDF is encrypted and needs a password. Please decrypt it first, resave without password and try again.')
        docs = PyMuPDFLoader(tmp, mode='single').load()
    elif ext in ['.txt', '.text', '.json', 'yaml', '.yml', '.ini']:
        docs = TextLoader(tmp).load()
    elif ext == '.md':
        # Use TextLoader for markdown files, but we'll use MarkdownTokenTextSplitter for splitting
        docs = TextLoader(tmp).load()
    elif ext == '.docx':
        docs = docx_load(filename, tmp)
    elif ext == '.doc':
        docs = doc_load(filename, tmp)
    elif ext == '.html':
        docs = UnstructuredHTMLLoader(tmp).load()
    elif ext == '.csv':
        docs = CSVLoader(tmp).load()
    elif ext in ['.xls', '.xlsx']:
        docs = UnstructuredExcelLoader(tmp, mode='single', find_subtable=False).load()
        # TODO use elements here and merge each doc group by doc.metadata['page_number'] to ensure
        # TODO each doc contains one sheet ?
    elif ext == '.xml':
        docs = UnstructuredXMLLoader(tmp).load()
    # elif ext == '.json':
    #     docs = JSONLoader(save_to_tmp_file(data)).load_and_split()
    else:
        raise HTTPException(400, f'File format not recognized: {filename}')

    for doc in docs:
        doc.metadata['source'] = filename
        if additional_meta is not None and isinstance(additional_meta, dict):
            doc.metadata.update(additional_meta.copy())

    try:
        os.remove(tmp)
    except Exception as e:
        logger.exception(e)
    return docs


def load_and_split_file(
    data, filename: str, additional_meta=None, splitter=None
) -> tuple[list[langchain.schema.Document], list[langchain.schema.Document]]:
    all_docs = load_file(data, filename, additional_meta)

    _, ext = os.path.splitext(filename)

    # If no splitter is provided, get an appropriate one based on file extension
    if splitter is None:
        splitter = get_text_splitter(file_ext=ext)

    if additional_meta.get('source_type') == models.SourceType.CORRECTED_TXT:
        splitter = get_corrected_answer_chunker()

    result_docs = split_docs_flat(all_docs, splitter)

    return all_docs, result_docs


def doc_load(original_filename, path) -> list[Document]:
    doc_converter = SharedConfig().doc_converter
    # Load via pdf conversion
    doc_data = open(path, 'rb').read()
    docx_data = doc_converter.convert_file(
        original_filename, 'doc', 'docx', doc_data,
    )

    docx_path = save_to_tmp_file(docx_data, '.docx')
    docs = docx_load(original_filename, docx_path)
    try:
        os.remove(docx_path)
    except Exception as e:
        logger.exception(e)

    return docs


def get_pdf_page_count(pdf_path=None, stream=None):
    doc = pymupdf.open(filename=pdf_path, stream=stream, filetype='pdf')
    num = doc.page_count
    del doc
    return num


def pdf_contains_images(pdf_path=None, stream=None):
    doc = pymupdf.open(filename=pdf_path, stream=stream, filetype='pdf')
    for page in range(doc.page_count):
        if doc.get_page_images(page):
            return True
    return False


def get_pdf_image_map(pdf_path=None, stream=None):
    doc = pymupdf.open(filename=pdf_path, stream=stream, filetype='pdf')
    image_map = {}
    for page in range(doc.page_count):
        image_map[page] = bool(doc.get_page_images(page))
    return image_map


def pdf_load_pages(path: str) -> list[Document]:
    return PyMuPDFLoader(path, mode='page').load()


def docx_load(original_filename, path) -> list[Document]:
    # unzip the docx in memory
    zipf = zipfile.ZipFile(path)
    filelist = zipf.namelist()
    with_images = False
    for fname in filelist:
        _, extension = os.path.splitext(fname)
        if extension in [".jpg", ".jpeg", ".png", ".bmp"]:
            with_images = True
            break

    if with_images:
        # Load via pdf conversion
        doc_converter = SharedConfig().doc_converter
        docx_data = open(path, 'rb').read()
        pdf_data = doc_converter.convert_file(
            original_filename, 'docx', 'pdf', docx_data,
        )

        try:
            docs = load_file(pdf_data, original_filename + '.pdf')
            logger.info(f"[LOADER] Done load PDF.")
        except Exception as e:
            logger.exception(e)
            raise
    else:
        docs = Docx2txtLoader(path).load()

    return docs


def parse_as_text(data: str | bytes, filename: str, file_id: int = None, page: int = None, total_pages: int = None) -> list[langchain.schema.Document]:
    docs = load_file(data, 'filename.txt', {'file_id': file_id})
    # Overwrite to original filename source
    for doc in docs:
        doc.metadata['source'] = filename
        if page:
            doc.metadata['page'] = page
        if total_pages:
            doc.metadata['total_pages'] = page

    return docs


def save_to_tmp_file(data, suffix=None):
    fname = tempfile.mktemp(suffix=suffix)
    with open(fname, 'wb') as f:
        f.write(data)
    return fname


def get_text_chunks(text):
    text_splitter = get_text_splitter()
    chunks = text_splitter.split_text(text)
    return chunks


def split_docs(docs: list, splitter=get_text_splitter()) -> list[list]:
    splitted = []
    for doc in docs:
        splitted.append(splitter.split_documents([doc]))

    return splitted


def split_docs_flat(docs: list, splitter=get_text_splitter()) -> list[Document]:
    return splitter.split_documents(docs)


def get_pdf_text(pdf_docs):
    text = ""
    for pdf in pdf_docs:
        pdf_reader = PdfReader(pdf)
        for page in pdf_reader.pages:
            text += page.extract_text()
    return text
