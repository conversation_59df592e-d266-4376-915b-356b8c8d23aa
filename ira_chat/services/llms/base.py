import abc
import asyncio
import functools
import json
import logging
import os
import time
from typing import List, Optional, Any, Dict, Union
from uuid import UUID

import tiktoken
from braintrust import init_logger
from braintrust.wrappers.langchain import BraintrustTracer
from langchain.callbacks.tracers import LangChainTracer
from langchain_core.callbacks import BaseCallbackHandler
from langchain_core.language_models import BaseChatModel, LanguageModelInput
from langchain_core.messages import BaseMessage
from langchain_core.outputs import LLMResult, GenerationChunk, ChatGenerationChunk
from langchain_core.runnables import RunnableConfig
from langchain_openai import ChatOpenAI
from langfuse.callback import CallbackHandler

from ira_chat.db import api as db_api
from ira_chat.db import base as db_base
from ira_chat.db import models
from ira_chat.utils import llm_utils
from ira_chat.utils.utils import generate_password

logger = logging.getLogger(__name__)


class InitLLM:
    provides_embeddings = True
    provides_llm = True

    def __init__(self, org_config: dict, params: dict, org_id: int, workspace_id: int = None):
        self.llm_params = llm_utils.get_llm_params(org_config)
        self.llm_type = self.llm_params.pop('llm_type')
        self.need_vision = params.get('need_vision', False)
        self.skip_callbacks = params.get('skip_callbacks', False)
        self.org_config = org_config
        self.workspace_id = workspace_id
        self.org_id = org_id
        self.model_name = None

        self.default_max_retries = 10
        self.default_temperature = 0.5

        params = self._preprocess_params(params)
        self.params = params

        self.callback_params = {
            'app_type': params.get('app_type', 'chat'),
            'app_id': params.get('app_id', 0),
            'object_id': params.get('object_id', 0),
            'workspace_id': workspace_id,
            'org_id': org_id,
            'llm_type': self.llm_type,
            'model_name': params.get('model_name'),
            'init_llm_obj': self,
        }

    @staticmethod
    def _preprocess_params(params: dict):
        if params.get('model_name'):
            params['model_name'] = params['model_name'].strip()
        if params.get('vision_model'):
            params['vision_model'] = params['vision_model'].strip()
        return params

    def get_callbacks(self):
        if self.skip_callbacks:
            return []
        callbacks = [SystemMetricsCallbackHandler(**self.callback_params)]

        return callbacks

    @staticmethod
    def get_embedding_model_params(org_config: dict | None = None):
        return llm_utils.get_llm_params(org_config)

    def init_llm(self, params: dict):
        params = self._preprocess_params(params)
        llm = self._init_llm(params)
        if not llm.metadata:
            llm.metadata = {}
        # llm.metadata['init_llm'] = self
        if hasattr(llm, 'model_name'):
            self.model_name = llm.model_name
        elif hasattr(llm, 'model'):
            self.model_name = llm.model
        return llm

    @abc.abstractmethod
    def _init_llm(self, params: dict) -> BaseChatModel:
        pass

    @abc.abstractmethod
    def init_streaming_llm(self, params: dict) -> BaseChatModel:
        pass

    @abc.abstractmethod
    def init_embeddings(self, override: dict = None):
        pass

    @staticmethod
    @abc.abstractmethod
    def available_models():
        """Each child class must implement and return a list of actual up-to-date models.

        Returns dict
        {
            'models': [],
            'vision_models': [],
            'embedding_models': [],
            'embedding_providers': [],
        }
        -------

        """
        pass

    @staticmethod
    def token_limit_per_model():
        """

        Returns collections.defaultdict {model: (context_limit, output_limit)}
        -------

        """
        pass

    def token_limits(self, model_name=None):
        model_name = model_name or self.model_name
        return self.token_limit_per_model()[model_name]

    @classmethod
    @abc.abstractmethod
    def compute_cost(cls, messages: List[List[BaseMessage]], output, input_tokens, output_tokens, model_name):
        pass


# noinspection PyProtectedMember
class BatchLLM:
    def __init__(self, llm: ChatOpenAI):
        self.llm = llm
        self.client = llm.client._client

    def batch(
        self,
        inputs: List[LanguageModelInput],
        input_ids: Optional[list[str]] = None,
        config: Optional[Union[RunnableConfig, List[RunnableConfig]]] = None,
        *,
        metadata: dict = None,
        **kwargs: Any,
    ):
        if not inputs:
            return []

        if not input_ids:
            input_ids = [generate_password() for _ in range(len(inputs))]
        payloads = []
        for i, query in enumerate(inputs):
            base_payload = self.llm._get_request_payload(query)
            payload = {
                "custom_id": input_ids[i],
                "method": "POST",
                "url": "/v1/chat/completions",
                "body": base_payload,
            }
            payloads.append(payload)

        data = "\n".join(json.dumps(payload) for payload in payloads)
        # print(data)
        file_name = f'batch_input_{generate_password().lower()}.jsonl'
        batch_input_file = self.client.files.create(
            file=(file_name, data.encode()),
            purpose="batch"
        )
        batch = self.client.batches.create(
            input_file_id=batch_input_file.id,
            endpoint="/v1/chat/completions",
            completion_window="24h",
            metadata=metadata,
        )
        return batch

    def status(self, batch_id: str):
        batch = self.client.batches.retrieve(batch_id)

        return batch.status
    
    def retrieve(
        self,
        batch_id: str,
        custom_ids: Optional[list[str]] = None,
        cleanup_files: bool = False,
    ) -> List[BaseMessage]:
        batch = self.client.batches.retrieve(batch_id)

        file_response = self.client.files.content(batch.output_file_id)
        query_set = set(custom_ids if custom_ids else [])
        results = []
        for json_line in file_response.iter_lines():
            resp = json.loads(json_line)
            if custom_ids and resp['custom_id'] in query_set:
                results.append((resp['custom_id'], self.llm._create_chat_result(resp['response']['body'])))
            elif not custom_ids:
                results.append((resp['custom_id'], self.llm._create_chat_result(resp['response']['body'])))

        if custom_ids:
            order = {k: i for i, k in enumerate(custom_ids)}
            results = sorted(results, key=lambda x: order[x[0]])

        results = [x[1] for x in results]

        if cleanup_files:
            self.client.files.delete(batch.input_file_id)
            self.client.files.delete(batch.output_file_id)

        return [r.generations[0].message for r in results]


class SystemMetricsCallbackHandler(BaseCallbackHandler):
    def __init__(self, workspace_id, org_id, app_id, app_type, llm_type, model_name, object_id=None, init_llm_obj=None):
        self.workspace_id = workspace_id
        self.org_id = org_id
        self.app_id = app_id
        self.app_type = app_type
        # Object id is the real item id (chat message / detection item)
        self.object_id = object_id
        self.llm_type = llm_type
        self.model_name = model_name
        self.init_llm: InitLLM = init_llm_obj
        self.runs = {}

    def on_chat_model_start(
        self,
        serialized: Dict[str, Any],
        messages: List[List[BaseMessage]],
        *,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        tags: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> Any:
        # model_name = kwargs.get('invocation_params', {}).get('model')
        # if not model_name:
        #     model_name = kwargs.get('invocation_params', {}).get('model_name')
        self.runs[run_id] = [messages, time.time()]

    def on_llm_start(
        self,
        serialized: Dict[str, Any],
        prompts: List[str],
        *,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        tags: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> Any:
        # Does not work
        pass

    def on_chain_error(
        self,
        error: BaseException,
        *,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        **kwargs: Any,
    ) -> Any:
        pass

    def on_llm_error(
        self,
        error: BaseException,
        *,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        **kwargs: Any,
    ) -> Any:
        """Run when LLM errors."""
        pass

    async def on_llm_end(
        self,
        response: LLMResult,
        *,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        **kwargs: Any,
    ) -> Any:
        messages_list, start_time = self.runs[run_id]
        response_time = time.time() - start_time
        model_name = self.model_name
        output = response.generations[0]
        if response.llm_output and response.llm_output.get('token_usage') and response.llm_output['token_usage'].get('completion_tokens'):
            # OpenAI case
            input_tokens = response.llm_output['token_usage']['prompt_tokens']
            output_tokens = response.llm_output['token_usage']['completion_tokens']
            model_name = response.llm_output['model_name']
        elif response.llm_output and response.llm_output.get('usage'):
            # Anthropic vision case
            usage = response.llm_output['usage']
            if isinstance(usage, dict):
                input_tokens = usage['input_tokens']
                output_tokens = usage['output_tokens']
            else:
                input_tokens = usage.input_tokens
                output_tokens = usage.output_tokens
            # Anthropic model
            model_name = response.llm_output['model']
        else:
            # Compute token count manually
            messages = messages_list[0]
            full_text_input = []
            for m in messages:
                if isinstance(m.content, str):
                    full_text_input.append(m.content)
                elif isinstance(m.content, list):
                    for inner in m.content:
                        if isinstance(inner, dict) and inner['type'] == 'text':
                            full_text_input.append(inner['text'])
                        # TODO detect and calculate image tokens here

            full_text_input = '\n'.join(full_text_input)
            full_text_output = '\n'.join(m.text for m in output)

            enc = tiktoken.get_encoding("o200k_base")
            input_tokens = len(enc.encode(full_text_input))
            output_tokens = len(enc.encode(full_text_output))

        cost = self.init_llm.compute_cost(messages_list, output, input_tokens, output_tokens, model_name)
        cost = round(cost, 10)

        await update_metrics(
            request_num=1,
            response_time=response_time,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            tokens_second=output_tokens / response_time,
            cost=cost,
            workspace_id=self.workspace_id,
            org_id=self.org_id,
            app_type=self.app_type,
            app_id=self.app_id,
            object_id=self.object_id,
            model_name=model_name,
        )


class StreamingCallbackHandler(BaseCallbackHandler):
    def __init__(self, queue: asyncio.Queue, event: asyncio.Event):
        self.runs = {}
        self.q = queue
        self.done = event

    async def on_chat_model_start(
        self,
        serialized: Dict[str, Any],
        messages: List[List[BaseMessage]], *,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        tags: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        **kwargs: Any
    ) -> Any:
        model_name = kwargs.get('invocation_params', {}).get('model')
        self.runs[run_id] = [messages, model_name]

    async def on_llm_start(
        self, serialized: Dict[str, Any], prompts: List[str], **kwargs: Any
    ) -> None:
        # If two calls are made in a row, this resets the state
        self.done.clear()

    async def on_llm_end(self, response: LLMResult, **kwargs: Any) -> None:
        generations = response.generations
        if len(generations) > 0 and len(generations[0]) > 0:
            if generations[0][0].message.content:
                self.done.set()
        else:
            self.done.set()

    async def on_llm_error(self, error: BaseException, **kwargs: Any) -> None:
        msg = f'{error.__class__.__name__}: {str(error)}'
        print(f'ERROR DURING STREAM: {msg}')
        self.q.put_nowait({'event_status': models.STATUS_ERROR, 'event_type': 'error', 'content': msg})
        self.done.set()

    async def on_llm_new_token(
        self,
        token: str,
        *,
        chunk: Optional[Union[GenerationChunk, ChatGenerationChunk]] = None,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        **kwargs: Any,
    ) -> Any:
        # msg = token.replace("\n", "\\n")
        # print(f'GOT "{msg}"')
        if token is not None and token != "":
            self.q.put_nowait({'event_status': 'OK', 'event_type': 'chunk', 'content': token})


async def update_metrics(
    request_num: int, response_time: float, input_tokens: int,
    output_tokens: int, tokens_second: float, cost: float,
    workspace_id: int | None, org_id: int, app_type, app_id, object_id: str = None, model_name: str = None
):
    metric_data = [
        (models.METRIC_REQUEST, request_num),
        (models.METRIC_RESPONSE_TIME, response_time),
        (models.METRIC_INPUT_TOKENS, input_tokens),
        (models.METRIC_OUTPUT_TOKENS, output_tokens),
        (models.METRIC_TOKENS_SECOND, tokens_second),
        (models.METRIC_COST_DOLLARS, cost)
    ]
    created_at = models.now()

    session = db_base.get_session()

    try:
        for metric, value in metric_data:
            if value is None:
                continue
            await db_api.create_metric({
                'value': value,
                'type': metric,
                'app_id': app_id,
                'object_id': object_id,
                'app_type': app_type,
                'workspace_id': workspace_id,
                'org_id': org_id,
                'extra': {"model_name": model_name},
                'created_at': created_at,
                'updated_at': created_at,
            }, session=session)
            # logger.info(f'Got metric {metric}={value}')
    except:
        await session.rollback()
        raise
    else:
        await session.commit()

    await session.close()


@db_base.session_aware()
async def update_metrics_async(
    request_num: int | None,
    response_time: float,
    input_tokens: int | None,
    output_tokens: int | None,
    tokens_second: float | None,
    cost: float | None,
    workspace_id: int | None,
    org_id: int,
    app_type,
    app_id,
    object_id: str = None,
    model_name: str = None,
    session=None
):
    metric_data = [
        (models.METRIC_REQUEST, request_num),
        (models.METRIC_RESPONSE_TIME, response_time),
        (models.METRIC_INPUT_TOKENS, input_tokens),
        (models.METRIC_OUTPUT_TOKENS, output_tokens),
        (models.METRIC_TOKENS_SECOND, tokens_second),
        (models.METRIC_COST_DOLLARS, cost)
    ]
    created_at = models.now()

    extra = {"model_name": model_name} if model_name else None
    for metric, value in metric_data:
        if value is None:
            continue
        await db_api.create_metric({
            'value': value,
            'type': metric,
            'app_id': app_id,
            'object_id': object_id,
            'app_type': app_type,
            'workspace_id': workspace_id,
            'org_id': org_id,
            'extra': extra,
            'created_at': created_at,
            'updated_at': created_at,
        }, session=session)


def write_metrics(workspace_id: int, org_id: int, app_type, app_id, object_id: str = None, model_name: str = None):
    def decorator(func):
        async def inner(*args, **kwargs):
            t = time.time()

            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                loop = asyncio.get_event_loop()
                partial = functools.partial(func, **kwargs)
                result = await loop.run_in_executor(None, partial, *args)

            response_time = time.time() - t
            await update_metrics_async(
                request_num=None,
                response_time=response_time,
                input_tokens=None,
                output_tokens=None,
                tokens_second=None,
                cost=None,
                workspace_id=workspace_id,
                org_id=org_id,
                app_type=app_type,
                app_id=app_id,
                object_id=object_id,
                model_name=model_name,
            )
            return result
        return inner

    return decorator


def get_langsmith_project():
    env = os.getenv("ENV_PREFIX")
    langchain_project = os.getenv("LANGCHAIN_PROJECT", "langsmith")
    return f"{env}-{langchain_project}" if env else langchain_project


def get_braintrust_project():
    env = os.getenv("ENV_PREFIX")
    braintrust_project = os.getenv("BRAINTRUST_PROJECT", "braintrust")
    return f"{env}-{braintrust_project}" if env else braintrust_project


def get_trace_callbacks():
    callbacks = []
    if os.getenv("LANGFUSE_SECRET_KEY") and os.getenv("LANGFUSE_PUBLIC_KEY"):
        # Add Langfuse callback handler
        langfuse_handler = CallbackHandler(
            secret_key=os.getenv("LANGFUSE_SECRET_KEY"),
            public_key=os.getenv("LANGFUSE_PUBLIC_KEY"),
            # host="https://cloud.langfuse.com",   # 🇪🇺 EU region
            host=os.getenv("LANGFUSE_HOST", "https://us.cloud.langfuse.com"),  # 🇺🇸 US region
        )
        callbacks.append(langfuse_handler)
    if os.getenv("LANGCHAIN_API_KEY"):
        langsmith_tracer = LangChainTracer(project_name=get_langsmith_project())
        callbacks.append(langsmith_tracer)

    if os.getenv('BRAINTRUST_API_KEY'):
        bt_logger = init_logger(project=get_braintrust_project())
        braintrust_tracer = BraintrustTracer(logger=bt_logger)
        callbacks.append(braintrust_tracer)

    return callbacks
