import collections
import logging
from typing import List

from langchain_core.language_models import BaseChatModel
from langchain_core.messages import BaseMessage
from langchain_openai import AzureChatOpenAI, ChatOpenAI, OpenAIEmbeddings

from ira_chat.services.llms import base
from ira_chat.utils.utils import llm_vision_model_by_llm_type

logger = logging.getLogger(__name__)


class OpenAI(base.InitLLM):
    def _init_llm(self, params: dict):
        self.llm_params.pop('api_type', None)
        self.llm_params.pop('api_version', None)
        self.llm_params.pop('api_base', None)
        self.llm_params.pop('deployment_name', None)
        self.llm_params.pop('organization', None)
        if self.need_vision:
            if params.get('vision_model'):
                params['model_name'] = params.get('vision_model')
            else:
                params['model_name'] = llm_vision_model_by_llm_type(self.llm_type)

        logger.info("INIT OPENAI")

        params = dict(
            model_name=params['model_name'],
            timeout=int(params['request_timeout']),
            max_retries=int(params.get('max_retries', self.default_max_retries)),
            temperature=float(params.get('temperature') or self.default_temperature),
            max_completion_tokens=params.get('max_output_tokens'),
            callbacks=self.get_callbacks(),
            api_key=self.llm_params.get('api_key'),
            top_p=float(params.get('top_p', 1.0)),
            frequency_penalty=float(params.get('frequency_penalty', 0.0)),
            presence_penalty=float(params.get('presence_penalty', 0.0)),
        )
        # if params['model_name'].startswith('o'):
        #     params['max_completion_tokens'] = params.get('max_output_tokens')
        # else:
        #     params['max_tokens'] = params.get('max_output_tokens')
        llm = ChatOpenAI(**params)
        return llm

    @staticmethod
    def _preprocess_params(params: dict):
        params = super(OpenAI, OpenAI)._preprocess_params(params)
        # Skip for embeddings
        if params.get('model_name', '').startswith('o'):
            params['temperature'] = 1
            params['top_p'] = 1
            params['frequency_penalty'] = 0
            params['presence_penalty'] = 0

        model_name = params.get('model_name', '')
        vision_model = params.get('vision_model', '')
        if model_name == 'gpt-4o-2024-05-13' or vision_model == 'gpt-4o-2024-05-13':
            max_output_tokens = params.get('max_output_tokens')
            if max_output_tokens and max_output_tokens > 4096:
                params['max_output_tokens'] = 4096
                params['max_tokens'] = 4096
                logger.warning(f"Reducing max_output_tokens to 4096 for gpt-4o-2024-05-13, got {max_output_tokens}")
        # if params.get('model_name', '')[:2] in ['o1']:
        #     params['disabled_params'] = {"parallel_tool_calls": None}
        return params

    def init_streaming_llm(self, params: dict) -> BaseChatModel:
        stream_callback = base.StreamingCallbackHandler(params['stream_queue'], params['stream_event'])
        llm = self.init_llm(params)
        llm.streaming = True
        llm.callbacks.append(stream_callback)

        return llm

    def init_embeddings(self, override: dict = None):
        logger.info("INIT EMBEDDING OPENAI")

        params = self.get_embedding_model_params(self.org_config)
        params.pop('api_type', None)
        params.pop('api_version', None)
        params.pop('api_base', None)
        params.pop('deployment_name', None)
        params.pop('organization', None)
        if override:
            params.update(override)

        return OpenAIEmbeddings(
            api_key=params.get('api_key'),
            model=params.get('embedding_model') or 'text-embedding-3-small',
            # text-embedding-3-small, text-embedding-3-large, text-embedding-ada-002
        )

    @staticmethod
    def available_models():
        return {
            'models': [
                'gpt-4o',
                'gpt-4o-mini',
                'gpt-4.1',
                'gpt-4.1-mini',
                'gpt-4.1-nano',
                'gpt-4.5-preview',
                'o1',
                'o1-mini',
                'o3-mini',
                'o4-mini',
            ],
            'vision_models': [
                'gpt-4o',
                'gpt-4o-mini',
                'gpt-4.1',
                'gpt-4.1-mini',
                'gpt-4.1-nano',
                'gpt-4.5-preview',
                'o4-mini',
            ],
            'embedding_models': [
                'text-embedding-ada-002',
                'text-embedding-3-small',
                'text-embedding-3-large',
            ],
        }

    @staticmethod
    def prices_per_model() -> dict:
        # openai models, price for 1M tokens
        prices = collections.defaultdict(lambda: [10, 30])
        prices.update({
            'gpt-3.5-turbo': [0.5, 1.5],
            'gpt-3.5-turbo-1106': [1, 2],
            'gpt-3.5-turbo-0125': [0.5, 1.5],
            'gpt-3.5-turbo-instruct': [1.5, 2],
            'gpt-3.5-turbo-16k': [0.5, 1.5],
            'gpt-3.5-turbo-0613': [1.5, 2],
            'gpt-3.5-turbo-16k-0613': [3, 4],
            'gpt-4': [30, 60],
            'gpt-4-0613': [30, 60],
            'gpt-4-32k': [60, 120],
            'gpt-4-32k-0613': [60, 120],
            'gpt-4-turbo': [10, 30],
            'gpt-4-turbo-preview': [10, 30],
            'gpt-4-1106-preview': [10, 30],
            'gpt-4-0125-preview': [10, 30],
            'gpt-4-turbo-2024-04-09': [10, 30],
            'gpt-4-vision-preview': [10, 30],
            'gpt-4-1106-vision-preview': [10, 30],
            'gpt-4o': [2.5, 10],
            'gpt-4o-2024-08-06': [2.5, 10.0],
            'gpt-4o-2024-05-13': [5, 15],
            'gpt-4o-mini': [0.15, 0.6],
            'gpt-4o-mini-2024-07-18': [0.15, 0.6],
            'gpt-4.1': [2, 8],
            'gpt-4.1-2025-04-14': [2, 8],
            'gpt-4.1-mini': [0.4, 1.6],
            'gpt-4.1-mini-2025-04-14': [0.4, 1.6],
            'gpt-4.1-nano': [0.1, 0.4],
            'gpt-4.1-nano-2025-04-14': [0.1, 0.4],
            'gpt-4.5-preview': [75, 150],
            'chatgpt-4o-latest': [5, 15],
            'o1': [15, 60],
            'o1-2024-12-17': [15, 60],
            'o1-preview': [15, 60],
            'o1-preview-2024-09-12': [15, 60],
            'o1-mini': [1.1, 4.4],
            'o1-mini-2024-09-12': [1.1, 4.4],
            'o3-mini': [1.1, 4.4],
            'o3-mini-2025-01-31': [1.1, 4.4],
            'o4-mini': [1.1, 4.4],
            'o4-mini-2025-04-16': [1.1, 4.4],
        })
        return prices

    @staticmethod
    def token_limit_per_model():
        limits = collections.defaultdict(lambda: [128000, 4096])
        limits.update({
            'gpt-3.5-turbo': [16384, 4096],
            'gpt-3.5-turbo-1106': [16384, 4096],
            'gpt-3.5-turbo-0125': [16384, 4096],
            'gpt-3.5-turbo-16k': [16384, 4096],
            'gpt-3.5-turbo-0613': [4096, 4096],
            'gpt-3.5-turbo-16k-0613': [16384, 4096],
            'gpt-4': [8192, 4096],
            'gpt-4-0613': [8192, 4096],
            'gpt-4-32k': [32768, 4096],
            'gpt-4-32k-0613': [32768, 4096],
            'gpt-4-turbo': [128000, 4096],
            'gpt-4-turbo-preview': [128000, 4096],
            'gpt-4-1106-preview': [128000, 4096],
            'gpt-4-0125-preview': [128000, 4096],
            'gpt-4-turbo-2024-04-09': [128000, 4096],
            'gpt-4-vision-preview': [128000, 4096],
            'gpt-4-1106-vision-preview': [128000, 4096],
            'gpt-4o': [128000, 16384],
            'gpt-4o-2024-08-06': [128000, 16384],
            'gpt-4o-2024-05-13': [128000, 16384],
            'gpt-4o-mini': [128000, 16384],
            'gpt-4o-mini-2024-07-18': [128000, 16384],
            'gpt-4.1': [1_047_576, 32768],
            'gpt-4.1-2025-04-14': [1_047_576, 32768],
            'gpt-4.1-mini': [1_047_576, 32768],
            'gpt-4.1-mini-2025-04-14': [1_047_576, 32768],
            'gpt-4.1-nano': [1_047_576, 32768],
            'gpt-4.1-nano-2025-04-14': [1_047_576, 32768],
            'gpt-4.5-preview': [128000, 16384],
            'chatgpt-4o-latest': [128000, 16384],
            'o1': [200000, 100000],
            'o1-preview': [128000, 32768],
            'o1-preview-2024-09-12': [128000, 32768],
            'o1-mini': [128000, 65536],
            'o1-mini-2024-09-12': [128000, 65536],
            'o3-mini': [200000, 100000],
            'o3-mini-2025-01-31': [200000, 100000],
            'o4-mini': [200000, 100000],
            'o4-mini-2025-04-16': [200000, 100000],
            # 'test-context': [135, 50],
        })
        return limits

    @classmethod
    def compute_cost(cls, messages: List[List[BaseMessage]], output: str, input_tokens, output_tokens, model_name):
        default_prices = [10, 30]
        prices = cls.prices_per_model().get(model_name)
        if not prices:
            # search by prefix
            for key, value in cls.prices_per_model().items():
                if model_name.startswith(key):
                    prices = value
                    break
        if not prices:
            prices = default_prices

        multiplier = 1e6
        price_input, price_output = prices
        total_cost = input_tokens * price_input / multiplier + output_tokens * price_output / multiplier
        return total_cost


class BatchOpenAI(OpenAI):
    def init_streaming_llm(self, params: dict) -> BaseChatModel:
        raise NotImplementedError('Batch model is not working with streaming.')

    def _init_llm(self, params: dict):
        llm = super()._init_llm(params)
