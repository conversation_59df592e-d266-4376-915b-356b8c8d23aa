import asyncio
import copy
import itertools
import json
import logging
import os

from langchain_core.documents import Document
from langchain_core.vectorstores import VectorStore

from ira_chat.config.shared_config import SharedConfig
from ira_chat.db import api as db_api, models, base
from ira_chat.services import engine_manager, jobs, langchain_svc, dataset_utils
from ira_chat.services import file_formats
from ira_chat.services import file_processor
from ira_chat.services import llm as ira_llm
from ira_chat.services import vectorstore as ira_vectorstore
from ira_chat.services.dataset_utils import watch_dataset, watch_index
from ira_chat.services.langchain_svc import NoneVectorStore
from ira_chat.services.vectorstore import create_dataset_filter, delete_documents_with_filter
from ira_chat.services.vectorstore import safe_delete_collection_if_empty
from ira_chat.utils import utils, llm_utils
from ira_chat.utils.utils import build_status

logger = logging.getLogger(__name__)


class DatasetManager:
    def __init__(self, org: models.Organization, ds: models.Dataset, app_config=None):
        self.type = engine_manager.global_engine_type()
        self.engine_context = None
        self.ds = ds
        self.ws = ds
        self.org = org
        self.org_config = self.org.get_config()

        self._vectorstore = None
        self._init_llm, self._init_llm_params = None, None
        self.app_config = ds.get_config() if ds else {}
        # Override llm_type if it is set in app_config
        if self.app_config.get('llm_type'):
            self.org_config['llm_type'] = self.app_config['llm_type']

    def get_default_prompts(self) -> dict:
        metadata_prompt = file_processor.METADATA_PROMPT
        chunk_prompt = file_processor.CHUNK_METADATA_PROMPT
        return {
            'metadata_prompt': metadata_prompt,
            'chunk_metadata_prompt': chunk_prompt,
        }

    def _get_suffix(self, index: models.DatasetIndex, add_suffix: str | None = None):
        base_suffix = engine_manager.index_namespace_suffix(self.org, self.ds, index=index, suffix=None)
        index_config = index.get_config()
        if index_config.get('use_unified_collection', False):
            return f'{base_suffix}' if not add_suffix else f'{base_suffix}-{add_suffix}'
        return f'{base_suffix}-{index.name}' if not add_suffix else f'{base_suffix}-{index.name}-{add_suffix}'

    async def _get_index_vectorstore(self, index: models.DatasetIndex, add_suffix: str | None = None, only_if_exists=False):
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None, self._get_named_vectorstore,
            self._get_suffix(index, add_suffix), index.embedding_provider, index.get_config(), only_if_exists
        )

    def _get_named_vectorstore(self, suffix, override_llm_type=None, index_config=None, only_if_exists=False):
        org_config = self.org_config.copy()
        if override_llm_type:
            org_config['llm_type'] = override_llm_type
        return langchain_svc.copy_prepare_vectorstore(
            ira_vectorstore.get_vectorstore(self.type, org_config, self.org.id, self.ws.id, index_config),
            ira_vectorstore.get_index_namespace(suffix),
            only_if_exists=only_if_exists,
        )


    async def handle_new_index(self, index: models.DatasetIndex, **options):
        # Create new collection in vectorstore
        vs = await self._get_index_vectorstore(index)
        index_existing_files = options.get('index_existing_files', False)

        if index_existing_files:
            files = await db_api.list_dataset_files(dataset_id=self.ds.id)
            t = asyncio.create_task(self.index_files(index, files, vs))

    async def handle_existing_index(self, index: models.DatasetIndex):
        # Create new collection in vectorstore
        logger.info(f'Handling existing index {index.name}...')
        vs = await self._get_index_vectorstore(index)

        logger.info(f'Checking for files to index for index {index.name}...')
        files = await db_api.list_dataset_files(dataset_id=self.ds.id)
        logger.info(f'Found {len(files)} files for index {index.name}...')
        embeddings = await db_api.list_dataset_file_embeddings(index_id=index.id)
        logger.info(f'Found {len(embeddings)} embeddings for index {index.name}...')

        embeddings_set = {e.dataset_file_id for e in embeddings}
        embeddings_hashes = {e.dataset_file_hash for e in embeddings}
        files_set = {f.id for f in files}

        files_to_index = [f for f in files if f.id not in embeddings_set]
        files_to_reindex = [f for f in files if f.id in embeddings_set and f.hash not in embeddings_hashes]
        embeddings_to_delete = [
            e for e in embeddings
            if e.dataset_file_id in embeddings_set and e.dataset_file_id not in files_set
        ]

        await self.delete_index_embeddings(embeddings_to_delete, index, vs, delete_db=True, log=True)
        vs_corrected = await self._get_index_vectorstore(index, 'corrected')
        if vs_corrected:
            await self.delete_index_embeddings(embeddings_to_delete, index, vs_corrected, delete_db=True, log=True)

        if len(files_to_index) > 0:
            logger.info(f'Indexing {len(files_to_index)} files for index {index.name}...')
            t = asyncio.create_task(self.index_files(index, files_to_index, vs))
        if len(files_to_reindex) > 0:
            logger.info(f'Re-indexing {len(files_to_reindex)} files for index {index.name}...')
            t2 = asyncio.create_task(self.reindex_files(index, files_to_reindex))

        # Update index stats for 2 vector stores
        await dataset_utils.update_index_stats(
            index, [
                await self._get_index_vectorstore(index),
                await self._get_index_vectorstore(index, 'corrected', only_if_exists=True)
            ]
        )

    @jobs.job_tracker
    @watch_dataset()
    @watch_index()
    async def index_files(self, index: models.DatasetIndex, files: list[models.DatasetFile], vs: VectorStore = None):
        if not vs:
            vs = await self._get_index_vectorstore(index)

        for file in files:
            file = await self.process_file(file)
            if file.status['status'] == models.STATUS_ERROR:
                # Do not attempt to index files in error, keep them in error
                continue
            await self.index_file(file, index, vs, set_status=False)

    @jobs.job_tracker
    @watch_dataset()
    async def process_file_for_all_indexes(self, file: models.DatasetFile, reprocess=False, force_index_recreate=False):
        file = await self.process_file(file, reprocess=reprocess)
        if file.status['status'] == models.STATUS_ERROR:
            # Do not attempt to index files in error, keep them in error
            return file

        indexes = await db_api.list_dataset_indexes(dataset_id=self.ds.id)
        for i, index in enumerate(indexes):
            if index.is_locked():
                logger.info(f'Skipping index {index.name} as it is locked')
                continue

            await self.index_file(file, index, force_index_recreate=force_index_recreate)

        return file

    @jobs.job_tracker
    @watch_dataset()
    @watch_index()
    async def reindex_files(self, index: models.DatasetIndex, files: list[models.DatasetFile]):
        for file in files:
            await self.handle_delete_file_for_index(file, index)
            await self.index_file(file, index, force_index_recreate=True, set_status=False)

    async def process_file(
        self,
        file_db: models.DatasetFile,
        reprocess=False,
    ):
        logger.info(f'Processing file {file_db.name}')
        try:
            docs = await self._get_docs_from_file(file_db, reprocess=reprocess)
        except Exception as e:
            logger.exception(f'Failed processing file {file_db.name}: {str(e)}')
            status = build_status(models.STATUS_ERROR, str(e), string=False)
        else:
            status = build_status(models.STATUS_SUCCESS, string=False)

        logger.info(f'Done processing the file {file_db.name}, status: {status}')
        return await db_api.update_dataset_file(file_db, {'status': status})

    async def index_file(
        self,
        file_db: models.DatasetFile,
        index: models.DatasetIndex,
        vs: VectorStore = None,
        force_index_recreate=False,
        set_status=True,
    ):
        if not vs:
            vs = await self._get_index_vectorstore(index)
        if file_db.source_type == models.SourceType.CORRECTED_TXT:
            # Here create corrected answers vectorstore
            vs = await self._get_index_vectorstore(index, 'corrected')

        try:
            # Should be already processed at this time, just grab the docs
            docs = await self._get_docs_from_file(file_db, reprocess=False)

            ids = await self.index_docs_in_vectorstore(docs, index, vs, force_index_recreate)
            embs = [{'dataset_file_id': file_db.id, 'dataset_file_hash': file_db.hash, 'id': i, 'index_id': index.id} for i in ids]
            await db_api.create_dataset_file_embeddings(embs)
        except Exception as e:
            logger.exception(f'Failed index file {file_db.name} in index {index.name}: {str(e)}')
            status = build_status(models.STATUS_ERROR, str(e), string=False)
        else:
            status = build_status(models.STATUS_SUCCESS, string=False)

        # Update index stats for 2 vector stores
        await dataset_utils.update_index_stats(
            index, [
                await self._get_index_vectorstore(index),
                await self._get_index_vectorstore(index, 'corrected', only_if_exists=True)
            ])
        if set_status:
            logger.info(f"Change status to {status['status']} [index={index.name}, message={status['message']}]")
            await db_api.update_dataset_index(index, {'status': status})

        return status

    async def _get_docs_from_file(self, file_db: models.DatasetFile, reprocess=False):
        storage_name = get_storage_dataset_file_name(self.org.name, self.ds.name, file_db)
        processed_name = f'{storage_name}.processed'
        file_data = None
        processed_data = None
        if not reprocess:
            try:
                processed_data_raw = await SharedConfig().file_manager.read_and_get_data(processed_name)
                processed_data = processed_data_raw.decode()
            except Exception:
                reprocess = True
        if reprocess:
            file_data = await SharedConfig().file_manager.read_and_get_data(storage_name)

        if reprocess:
            processor = self.get_file_processor(file_db)
            _, docs = await processor.process(
                file_data, file_db.name, {'file_id': file_db.id, 'source_type': file_db.source_type}
            )
            docs_json = [d.model_dump(exclude_unset=True) for d in docs]
            await SharedConfig().file_manager.save_file(processed_name, json.dumps(docs_json).encode())
        else:
            logger.info(f"[FILE] Using already processed data for {file_db.name}")
            docs = [Document.model_validate(d) for d in json.loads(processed_data)]

        return docs

    def get_file_processor(self, file_db: models.DatasetFile):
        async def update_status_hook(status: str):
            logger.info(f"Change status to {status} [file={file_db.name}]")
            await db_api.update_dataset_file(file_db, {'status': utils.build_status(status)})

        async def set_metadata_hook(metadata: dict):
            logger.info(f"Metadata extracted [file={file_db.name}]: {metadata}")
            await db_api.update_dataset_file(file_db, {'meta': metadata})

        doc_metadata_hook = self.app_config.get('metadata_hook') or 'llm_hook'
        # TODO: disable chunk metadata extraction for now
        chunk_metadata_hook = self.app_config.get('chunk_metadata_hook') or 'noop_hook'

        metric_config = {
            'app_type': models.APP_TYPE_DATASET_FILE,
            'app_id': 0,
            'object_id': file_db.id,
        }
        return file_processor.FileProcessor(
            llm=llm_utils.set_cheapest_model(self.get_llm(self._override_config(metric_config))),
            # llm=self.get_llm(self._override_config(metric_config)),
            config=self.app_config,
            update_status_hook=update_status_hook,
            doc_metadata_hook=doc_metadata_hook,
            chunk_metadata_hook=chunk_metadata_hook,
            set_metadata_hook=set_metadata_hook,
        )

    async def index_docs_in_vectorstore(
        self,
        docs: list[Document],
        index: models.DatasetIndex,
        vs: VectorStore = None,
        force_index_recreate=False
    ) -> list[str]:
        # await db_api.update_dataset_index(index, {'status': build_status(models.STATUS_PROCESSING, string=False)})
        try:
            ids = await self._index_docs_in_vectorstore(docs, index, vs, force_index_recreate)
        except Exception as e:
            logger.error(f'Failed to index documents in vectorstore: {str(e)}')
            logger.info(f"Change status to ERROR [index={index.name}, message={str(e)}]")
            await db_api.update_dataset_index(index, {'status': build_status(models.STATUS_ERROR, str(e), string=False)})
            raise
        else:
            pass
            # await db_api.update_dataset_index(index, {'status': build_status(models.STATUS_SUCCESS, string=False)})

        return ids

    async def _index_docs_in_vectorstore(
        self,
        docs: list[Document],
        index: models.DatasetIndex,
        vs: VectorStore = None,
        force_index_recreate=False
    ) -> list[str]:
        if not vs:
            vs = await self._get_index_vectorstore(index)

        if force_index_recreate:
            vs = langchain_svc.prepare_vectorstore(
                vs,
                ira_vectorstore.get_index_namespace(self._get_suffix(index)),
                index_recreate=force_index_recreate,
            )

        index_type = index.config.get('index_type', engine_manager.INDEX_NORMAL)
        if index_type == engine_manager.INDEX_PARENT:
            # set ids
            for doc in docs:
                id = utils.generate_unicode_uuid()
                doc.metadata['id'] = id
            # child split
            child_docs = file_formats.split_docs(docs, splitter=file_formats.get_child_splitter())
            # add parent_id to child docs
            for child_list in child_docs:
                for child in child_list:
                    child.metadata['parent_id'] = child.metadata.pop('id')

            # index child docs
            all_child_docs = list(itertools.chain.from_iterable(child_docs))
            ids = [utils.generate_unicode_uuid() for _ in all_child_docs]
            # Send ids because otherwise ids somehow have different values:
            # auto-generated (and returned) vs. inserted in vectorstore
            ids = await vs.aadd_documents(all_child_docs, ids=ids)
            # store parent data
            async with base.session_context():
                for doc in docs:
                    await db_api.create_dataset_file_document({
                        'doc_metadata': doc.metadata,
                        'content': utils.clean_string(doc.page_content),
                        'dataset_file_id': doc.metadata['file_id'],
                        'id': doc.metadata['id'],
                        'index_id': index.id,
                    })
            # return child doc ids
            return ids
        else:
            ids = [utils.generate_unicode_uuid() for _ in docs]
            # Send ids because otherwise ids somehow have different values:
            # auto-generated (and returned) vs. inserted in vectorstore

            ids = await vs.aadd_documents(docs, ids=ids)
            return ids

    async def delete_index_embeddings(
        self,
        embeddings: list[models.DatasetFileEmbeddings],
        index: models.DatasetIndex,
        vs: VectorStore = None,
        delete_db: bool = False,
        log: bool = False,
    ):
        ids = [e.id for e in embeddings]
        if not ids:
            return

        if log:
            logger.info(f'Deleting {len(ids)} embeddings associated with index {index.name}...')

        if not vs:
            vs = await self._get_index_vectorstore(index)
        await vs.adelete(ids)

        async with base.session_context() as session:
            if delete_db:
                await db_api.delete_dataset_file_embeddings(ids=ids, index_id=index.id)
                await db_api.delete_dataset_file_documents(file_ids=list(set(e.dataset_file_id for e in embeddings)))

            await session.flush()
            await dataset_utils.update_index_stats(index, [
                vs, await self._get_index_vectorstore(index, 'corrected', only_if_exists=True),
            ])

    async def handle_delete_file(self, file_db: models.DatasetFile):
        indexes = await db_api.list_dataset_indexes(dataset_id=self.ds.id)
        for index in indexes:
            if index.is_locked():
                continue
            await self.handle_delete_file_for_index(file_db, index)

        logger.info(f'Done deleting vectorstore embeddings associated with {file_db.name}.')

    async def handle_delete_file_for_index(self, file_db: models.DatasetFile, index: models.DatasetIndex):
        embeddings = await db_api.list_dataset_file_embeddings(file_id=file_db.id, index_id=index.id)

        logger.info(f'Deleting {len(embeddings)} [index={index.name}] embeddings associated with {file_db.name}...')
        if file_db.source_type == models.SourceType.CORRECTED_TXT:
            # Should exist anyway
            vs = await self._get_index_vectorstore(index, 'corrected')
        else:
            vs = await self._get_index_vectorstore(index)

        await self.delete_index_embeddings(embeddings, index, delete_db=True, vs=vs)

    async def delete_index(self, index: models.DatasetIndex):
        """
        Delete an index, using filtered deletion for unified collections.
        """
        index_config = index.get_config()

        if index_config.get('use_unified_collection', False):
            # Use filtered deletion for unified collections
            logger.info(f"Using filtered deletion for unified collection index {index.name}")

            vs = await self._get_index_vectorstore(index)
            vs_corrected = await self._get_index_vectorstore(index, 'corrected', only_if_exists=True)

            # Create filter for this dataset's documents
            dataset_filter = create_dataset_filter(self.ds.id)

            # Delete documents with filter from main vectorstore
            await delete_documents_with_filter(vs, dataset_filter)

            # Delete documents from corrected vectorstore if it exists
            if vs_corrected and vs_corrected is not NoneVectorStore:
                await delete_documents_with_filter(vs_corrected, dataset_filter)

            # Check if collections are now empty and delete if so
            main_deleted = await safe_delete_collection_if_empty(vs)
            corrected_deleted = False
            if vs_corrected and vs_corrected is not NoneVectorStore:
                corrected_deleted = await safe_delete_collection_if_empty(vs_corrected)

            logger.info(
                f"Filtered deletion complete for index {index.name}. "
                f"Main collection deleted: {main_deleted}, "
                f"Corrected collection deleted: {corrected_deleted}"
            )
        else:
            # Use legacy deletion for individual collections
            logger.info(f"Using legacy deletion for individual collection index {index.name}")
            vs = await self._get_index_vectorstore(index)
            vs_corrected = await self._get_index_vectorstore(index, 'corrected', only_if_exists=True)
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, langchain_svc.delete_vectorstore, vs)
            if vs_corrected:
                await loop.run_in_executor(None, langchain_svc.delete_vectorstore, vs_corrected)

    def get_init_llm(self, config=None):
        if self._init_llm is not None:
            return self._init_llm, self._init_llm_params

        config = self._override_config(config)
        params = {
            'request_timeout': 300,
            'temperature': 0.2,
            'model_name': utils.llm_model_name_by_llm_type(self.org_config.get('llm_type', 'openai')),
            'vision_model': None,
            'mode': 'condense_question',
            'language': 'english',
            'max_tokens': None,
            'need_vision': None,
            'llm_type': None,
            'index_type': None,
        }
        if config:
            for k in config:
                params[k] = config[k]
        init_llm = ira_llm.init_llm_class(self.org_config, params, self.org.id, None)
        self._init_llm, self._init_llm_params = init_llm, params
        return init_llm, params

    def get_llm(self, config=None):
        init_llm, params = self.get_init_llm(config=config)
        return init_llm.init_llm(params)

    def get_embeddings(self, config=None):
        init_llm, params = self.get_init_llm(config=config)
        return init_llm.init_embeddings()

    def _override_config(self, new_config: dict = None):
        config = copy.deepcopy(self.app_config)
        if new_config:
            for k in new_config:
                config[k] = new_config[k]

        return config


def get_storage_dataset_file_name(org_name: str, dataset_name: str, file: models.DatasetFile):
    name, ext = os.path.splitext(file.name)
    return f'{org_name}/datasets/{dataset_name}/{file.name}/{file.id}{ext}'


def index_namespace_suffix(org: models.Organization, ws: models.Workspace | models.Dataset, suffix: str | None):
    if not suffix:
        return f'{org.name}-{ws.name}'
    else:
        return f'{org.name}-{ws.name}-{suffix}'
