import asyncio
import collections
import json
import logging
import mimetypes
import os
from typing import Optional, Literal

import pydantic
from fastapi import APIRouter, HTTPException
from fastapi import UploadFile
from fastapi.responses import ORJSONResponse, Response, StreamingResponse
from pydantic import BaseModel

from ira_chat.api import datasets as datasets_api, datasets
from ira_chat.config.shared_config import SharedConfig
from ira_chat.context import context as ctx
from ira_chat.db import api as db_api, models, base
from ira_chat.policies import policies
from ira_chat.services import engine_manager, dataset_utils
from ira_chat.services.dataset_manager import get_storage_dataset_file_name
from ira_chat.services.engine_manager import EngineManager
from ira_chat.utils import json_utils, utils, metric_utils

logger = logging.getLogger(__name__)


def get_router(prefix='/'):
    router = APIRouter(prefix=os.path.join(prefix, 'extractors'))

    router.add_api_route("", list_extractors, methods=['GET'], name='List extractors')
    # router.add_api_route("/metrics", list_extractor_metrics, methods=['GET'], name='List extractors metrics')
    router.add_api_route("", create_extractor, methods=['POST'], name='Create extractor')
    router.add_api_route("/{extractor_id}", get_extractor, methods=['GET'], name='Get extractor')
    router.add_api_route("/{extractor_id}", update_extractor, methods=['PUT'], name='Update extractor')
    router.add_api_route("/{extractor_id}", delete_extractor, methods=['DELETE'], name='Delete extractor')
    router.add_api_route("/{extractor_id}/process", process_extractor, methods=['POST'], name='Process extractor')
    router.add_api_route("/{extractor_id}/search", search_extractor, methods=['GET'], name='Search extractor')
    router.add_api_route("/{extractor_id}/results", list_extractor_results, methods=['GET'], name='List extractor results')
    router.add_api_route("/{extractor_id}/results/{result_id}", get_extractor_result, methods=['GET'], name='Get extractor result')
    router.add_api_route("/{extractor_id}/results/{result_id}/csv", get_extractor_result_csv, methods=['GET'], name='Get extractor result csv')
    router.add_api_route("/{extractor_id}/results/{result_id}/xlsx", get_extractor_result_xlsx, methods=['GET'], name='Get extractor result xlsx')
    router.add_api_route("/{extractor_id}/results/{result_id}", delete_extractor_result, methods=['DELETE'], name='Delete extractor result')
    router.add_api_route("/{extractor_id}/files", upload_file_extractor, methods=['POST'], name='Upload a file extractor')
    router.add_api_route("/{extractor_id}/files", list_files_extractor, methods=['GET'], name='List files extractor')
    router.add_api_route("/{extractor_id}/files/by-hash/{hash_id}", get_file_extractor_by_hash, methods=['GET'], name='Get file by hash')
    router.add_api_route("/{extractor_id}/files/{file_id}", get_file_extractor, methods=['GET'], name='Get a file extractor')
    router.add_api_route("/{extractor_id}/files/{file_id}", replace_file_extractor, methods=['PUT'], name='Replace a file extractor')
    router.add_api_route("/{extractor_id}/files/{file_id}/contents", get_file_extractor_contents, methods=['GET'], name='Get a file extractor index content')
    router.add_api_route("/{extractor_id}/files/{file_id}", delete_file_extractor, methods=['DELETE'], name='Delete a file extractor')
    router.add_api_route("/{extractor_id}/files/{file_id}/reindex", reindex_file_extractor, methods=['PUT'], name='Reindex a file extractor')
    router.add_api_route("/{extractor_id}/files/{file_id}/download", download_file_extractor, methods=['GET'], name='Download a file extractor')
    # router.add_api_route("/{extractor_id}/graph", get_extractor_graph, methods=['GET'], name='Get extractor graph metrics')
    # router.add_api_route("/{extractor_id}/items_metrics", get_items_metrics, methods=['GET'], name='Get all items metrics')
    router.add_api_route("/{extractor_id}/items", list_extractor_items, methods=['GET'], name='List items')
    router.add_api_route("/{extractor_id}/items", create_extractor_item, methods=['POST'], name='Post an item')
    router.add_api_route("/{extractor_id}/items/{item_id}", get_extractor_item, methods=['GET'], name='Get item')
    # router.add_api_route("/{extractor_id}/items/{item_id}/metrics", get_item_metrics, methods=['GET'], name='Get item metrics')
    # router.add_api_route("/{extractor_id}/items/{item_id}", edit_item, methods=['PUT'], name='Edit an item')

    router.add_api_route(
        "/{extractor_id}/items/{item_id}", delete_extractor_item, methods=['DELETE'], name='Delete a item'
    )

    return router


class Extractor(BaseModel):
    config: Optional[dict] = None
    meta: Optional[dict] = None
    data_schema: Optional[dict] = pydantic.Field(None, description='data schema')
    description: Optional[str] = None
    title: str


class ExtractorUpdate(BaseModel):
    config: Optional[dict] = None
    meta: Optional[dict] = None
    data_schema: Optional[dict] = pydantic.Field(None, description='data schema')
    description: Optional[str] = None
    title: Optional[str] = None


class ExtractorItemCreate(BaseModel):
    input: str
    is_schema: Optional[bool] = False


async def list_extractors(
    all: Optional[bool] = None,
    limit: int = 100,
    page: Optional[int] = None,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
):
    ses = ctx.current_session()
    ws = ctx.current_workspace()

    paginate = page is not None
    params = dict(
        workspace_id=ws.id,
        limit=limit,
        page=page or 1,
        order=order,
        desc=desc,
        q=q,
    )

    if not ws:
        raise HTTPException(400, 'Workspace is not set')

    async with base.session_context():
        show_hidden = all if all is True else False
        if not policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_VIEW):
            extractors, count = await db_api.list_extractors(owner_id=ses.user_id, show_hidden=show_hidden, **params)
            ex_dicts = [e.to_dict() for e in extractors]
        else:
            extractors, count = await db_api.list_extractors(show_hidden=show_hidden, **params)
            ex_dicts = [e.to_dict() for e in extractors]
            user_ids = list(set([c.owner_id for c in extractors]))
            if user_ids:
                users = await db_api.list_users(user_ids)
                user_map = {u.id: u for u in users}
            else:
                user_map = {}

            for ext in ex_dicts:
                if ext['owner_id'] in user_map:
                    ext['owner_login'] = user_map[ext['owner_id']].login
                    ext['owner_name'] = user_map[ext['owner_id']].name

        for i, ext in enumerate(ex_dicts):
            ext['dataset_name'] = extractors[i].get_dataset_index()[0]
        # if extractors:
            # datasets = await db_api.list_datasets_by_org_and_names(
            #     ctx.current_org().id, [e.get_dataset_index()[0] for e in extractors]
            # )
            # dataset_map = {d.name: d for d in datasets}
            # if datasets:
            #     files = await db_api.list_dataset_files(dataset_ids=[d.id for d in datasets])
            # else:
            #     files = []
        # else:
            # dataset_map = {}
            # files = []

    # file_map = collections.defaultdict(list)
    # [file_map[f.dataset_id].append(f.to_dict()) for f in files]

    # for ext_dict in ex_dicts:
    #     if ext_dict['dataset_name']:
    #         ext_dict['files'] = file_map[dataset_map[ext_dict['dataset_name']].id]

    if paginate:
        return ORJSONResponse(content={'items': ex_dicts, 'count': count, 'limit': limit, 'page': page})
    else:
        return ORJSONResponse(content=ex_dicts)


async def create_extractor(req: Extractor):
    ses = ctx.current_session()
    ws = ctx.current_workspace()

    config = req.config or {}

    async with base.session_context() as session:
        ext: models.Extractor = await db_api.create_extractor({
            'owner_id': ses.user_id,
            'status': utils.build_status(models.STATUS_CREATED, None),
            'has_updates': False,
            'workspace_id': ctx.current_workspace().id,
            'title': req.title,
            'config': config,
            'meta': req.meta,
            'schema': json.dumps(req.data_schema or ''),
            'description': req.description,
        })
        await session.flush()
        ext = await auto_create_dataset_index(ext, ws.get_config(), ctx.current_org().id, ses.user_id)

    return ORJSONResponse(content=ext.to_dict())


async def auto_create_dataset_index(ext, ws_config, org_id, owner_id):
    ext_config = ext.get_config()

    if ext_config.get('index'):
        if ext_config['index'] == 'null':
            ext_config['index'] = None
        else:
            return ext
    # Default to name/openai
    dataset_name = f'Extractor id={ext.id},name={ext.title}'
    extractor_ws_config = ws_config.get('apps_config', {}).get('extractor', {})
    llm_type = extractor_ws_config.get('llm_type') or ws_config.get('llm_type') or 'openai'
    ext_config['index'] = f'{dataset_name}/{llm_type}'
    index_name = llm_type

    try:
        dataset = await db_api.get_dataset_by_org_and_name(org_id, dataset_name)
    except Exception as e:
        # Create a dataset
        dataset = await db_api.create_dataset({
            'name': dataset_name,
            'display_name': dataset_name,
            'org_id': org_id,
            'description': f'{dataset_name}',
            'owner_id': owner_id,
            'scope': models.DATASET_SCOPE_EXTRACTOR,
            'status': utils.build_status(models.STATUS_SUCCESS),
        })

    try:
        index = await db_api.get_dataset_index_by_dataset_and_name(dataset.id, index_name)
    except Exception as e:
        # Detect vector dimension based on embedding provider
        vector_dimension = engine_manager.get_vector_dimension_for_config(
            org_config=ctx.current_org().get_config(),
            embedding_provider=llm_type,
            embedding_model=extractor_ws_config.get('embedding_model')
        )

        current_ws = ctx.current_workspace()

        # Configure for unified collection when creating extractor datasets
        index_config = {
            'index_type': engine_manager.INDEX_NORMAL,
            'use_unified_collection': True,
            'collection_type': 'UNIFIED_WORKSPACE',
            'workspace_id': current_ws.id,
            'workspace_name': current_ws.name,  # Store for collection naming
            'vector_dimension': vector_dimension,
            'embedding_model': extractor_ws_config.get('embedding_model'),
            'embedding_provider': llm_type
        }

        index = await db_api.create_dataset_index({
            'dataset_id': dataset.id,
            'name': index_name,
            'display_name': index_name,
            'description': f'Index for {index_name}',
            'config': index_config,
            'owner_id': owner_id,
            'org_id': org_id,
            'embedding_provider': llm_type,
            'status': utils.build_status(models.STATUS_SUCCESS),
        })

    ext = await db_api.update_extractor(ext, {'config': ext_config, 'auto_dataset_id': dataset.id})

    return ext


async def get_extractor(extractor_id: int):
    ext, ext_dict = await get_extractor_obj(extractor_id)
    ext_checked = await dataset_utils.dataset_index_check(ctx.current_org().id, ext)

    ext_dict['status'] = ext_checked.status

    return ORJSONResponse(content=ext_dict)


async def _get_extractor(extractor_id: int, edit=True) -> models.Extractor:
    async with base.session_context():
        ses = ctx.current_session()
        ws = ctx.current_workspace()
        check_permission = policies.AccessMode.ALL_CHATS_MANAGE if edit else policies.AccessMode.ALL_CHATS_VIEW
        all_chats_allowed = policies.is_allowed(ctx.current_permissions(), check_permission)
        ext = await db_api.get_extractor_by_id(extractor_id)

        if ses.user_id != ext.owner_id and not all_chats_allowed:
            raise HTTPException(403, 'Forbidden')
        if ws.id != ext.workspace_id:
            raise HTTPException(404, 'Not found')

    return ext


async def get_extractor_obj(extractor_id: int, session=None):
    async with base.session_context():
        ext = await _get_extractor(extractor_id, edit=False)
        dataset = await db_api.get_dataset_by_org_and_name(ctx.current_org().id, ext.get_dataset_index()[0], notfoundok=True)

        if dataset:
            files = await db_api.list_dataset_files(dataset_id=dataset.id, session=session)
            file_dicts = [f.to_dict() for f in files]
        else:
            file_dicts = []
        results = await db_api.list_extractor_results(extractor_id=ext.id, session=session)
        res_dicts = [r.to_dict() for r in results]

    ext_dict = ext.to_dict()
    ext_dict['dataset_name'] = dataset.name if dataset else None
    ext_dict['dataset_id'] = dataset.id if dataset else None
    ext_dict['files'] = file_dicts
    ext_dict['results'] = res_dicts

    return ext, ext_dict


async def update_extractor(
    extractor_id: int, req: ExtractorUpdate,
):
    async with base.session_context():
        ext = await _get_extractor(extractor_id, edit=True)

        req_update = req.model_dump(exclude_unset=True)
        if 'data_schema' in req_update:
            req_update['schema'] = json.dumps(req_update.pop('data_schema'))
        if 'config' in req_update:
            req_update['config'] = req_update['config']

        if req_update.get('config') and req_update['config'].get('index') != ext.config.get('index'):
            ext = await dataset_utils.dataset_index_check(ctx.current_org().id, ext)

        ext: models.Extractor = await db_api.update_extractor(ext, req_update)

    return ORJSONResponse(content=ext.to_dict())


async def delete_extractor(extractor_id: int, delete_dataset: Optional[bool] = False):
    tasks = []
    async with base.session_context():
        ext = await _get_extractor(extractor_id, edit=True)

        ds_name, index_name = dataset_utils.get_dataset_index_names(ext.get_config())
        dataset_id = None
        if ds_name:
            dataset = await db_api.get_dataset_by_org_and_name(ctx.current_org().id, ds_name, notfoundok=True)
            if dataset and delete_dataset:
                dataset_id = dataset.id
                tasks.append((datasets_api.clear_dataset, dataset.id))

        if ext.auto_dataset_id and dataset_id != ext.auto_dataset_id:
            tasks.append((datasets_api.clear_dataset, ext.auto_dataset_id))

        await db_api.delete_extractor_item_outputs(extractor_id=extractor_id)
        await db_api.delete_extractor_items(extractor_id)
        await db_api.delete_extractor_results(extractor_id)
        await db_api.delete_extractor(extractor_id)

    for func, arg in tasks:
        t = asyncio.create_task(func(arg))

    return Response(status_code=204)


async def process_extractor(extractor_id: int, wait_files: Optional[bool] = False):
    result = await process_extractor_internal(extractor_id, wait_files=wait_files)
    return ORJSONResponse(content=result.to_dict())


async def search_extractor(extractor_id: int, query: str, limit: int = 10):
    async with base.session_context():
        ext = await _get_extractor(extractor_id, edit=False)
        dataset_name, index_name = ext.get_dataset_index()
        dataset = await db_api.get_dataset_by_org_and_name(ctx.current_org().id, dataset_name)
        index = await db_api.get_dataset_index_by_dataset_and_name(dataset.id, index_name)

        # {'documents': [ Document() ]}
        return await datasets.search_in_dataset_index_internal(dataset, index, query, limit=limit)


async def process_extractor_internal(extractor_id: int, wait_files: Optional[bool] = False) -> models.ExtractorResult:
    ses = ctx.current_session()
    ws = ctx.current_workspace()

    ext = await _get_extractor(extractor_id, edit=True)

    manager = await EngineManager.init_async(ctx.current_org(), ws, app=ext)
    extractor_res = await manager.process_extractor(ext, ses.user_id, ctx.request.value.state.access_type, wait_files)

    return extractor_res


async def list_extractor_results(extractor_id: int):
    ses = ctx.current_session()
    ws = ctx.current_workspace()
    all_chats_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_VIEW)
    ext = await db_api.get_extractor_by_id(extractor_id)

    if ses.user_id != ext.owner_id and not all_chats_allowed:
        raise HTTPException(403, 'Forbidden')
    if ws.id != ext.workspace_id:
        raise HTTPException(404, 'Not found')

    results = await db_api.list_extractor_results(extractor_id=ext.id)
    res_dicts = [r.to_dict() for r in results]

    return ORJSONResponse(content=res_dicts)


async def get_extractor_result(extractor_id: int, result_id: int):
    ses = ctx.current_session()
    ws = ctx.current_workspace()
    all_chats_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_VIEW)
    ext = await db_api.get_extractor_by_id(extractor_id)

    if ses.user_id != ext.owner_id and not all_chats_allowed:
        raise HTTPException(403, 'Forbidden')
    if ws.id != ext.workspace_id:
        raise HTTPException(404, 'Not found')

    result = await db_api.get_extractor_result_by_id(result_id)
    if result.extractor_id != extractor_id:
        raise HTTPException(404, f'Not found for result id: {result_id}')

    return ORJSONResponse(content=result.to_dict())


async def get_extractor_result_csv(extractor_id: int, result_id: int):
    ses = ctx.current_session()
    ws = ctx.current_workspace()
    all_chats_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_VIEW)
    ext = await db_api.get_extractor_by_id(extractor_id)

    if ses.user_id != ext.owner_id and not all_chats_allowed:
        raise HTTPException(403, 'Forbidden')
    if ws.id != ext.workspace_id:
        raise HTTPException(404, 'Not found')

    result = await db_api.get_extractor_result_by_id(result_id)
    if result.extractor_id != extractor_id:
        raise HTTPException(404, f'Not found for result id: {result_id}')

    if result.output is None:
        raise HTTPException(422, 'Output is not available')
    csv_data = json_utils.json_to_csv(json.loads(result.output))

    return Response(
        csv_data,
        media_type='text/csv',
        headers={'Content-Disposition': f'attachment; filename="{ext.title}_{result_id}.csv"'}
    )


async def get_extractor_result_xlsx(extractor_id: int, result_id: int):
    ses = ctx.current_session()
    ws = ctx.current_workspace()
    all_chats_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_VIEW)
    ext: models.Extractor = await db_api.get_extractor_by_id(extractor_id)

    if ses.user_id != ext.owner_id and not all_chats_allowed:
        raise HTTPException(403, 'Forbidden')
    if ws.id != ext.workspace_id:
        raise HTTPException(404, 'Not found')

    result = await db_api.get_extractor_result_by_id(result_id)
    if result.extractor_id != extractor_id:
        raise HTTPException(404, f'Not found for result id: {result_id}')

    xlsx_data = json_utils.json_to_xlsx(json.loads(result.output))

    return Response(
        xlsx_data,
        media_type=mimetypes.guess_type('file.xlsx')[0],
        headers={'Content-Disposition': f'attachment; filename="{ext.title}_{result_id}.xlsx"'}
    )


async def delete_extractor_result(extractor_id: int, result_id: int):
    ses = ctx.current_session()
    ws = ctx.current_workspace()
    all_chats_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_VIEW)
    ext = await db_api.get_extractor_by_id(extractor_id)

    if ses.user_id != ext.owner_id and not all_chats_allowed:
        raise HTTPException(403, 'Forbidden')
    if ws.id != ext.workspace_id:
        raise HTTPException(404, 'Not found')

    result = await db_api.get_extractor_result_by_id(result_id)
    if result.extractor_id != extractor_id:
        raise HTTPException(404, f'Not found for result id: {result_id}')

    await db_api.delete_extractor_result(result.id)

    return Response(status_code=204)


async def upload_file_extractor(
    extractor_id: int,
    file: Optional[UploadFile],
    mode: Optional[Literal['llm', 'ocr']] = 'llm',
    timeline_id: Optional[int] = None,
):
    ws = ctx.current_workspace()

    ext = await _get_extractor(extractor_id, edit=True)

    dataset = await db_api.get_dataset_by_org_and_name(ctx.current_org().id, ext.get_dataset_index()[0])
    manager = await EngineManager.init_async(ctx.current_org(), ws, ext)

    file_data = file.file.read()
    file_db = await db_api.create_dataset_file({
        'dataset_id': dataset.id,
        'owner_id': ext.owner_id,
        'org_id': ctx.current_org().id,
        'size': file.size,
        'name': file.filename,
        'status': utils.build_status(models.STATUS_PROCESSING),
        'meta': {},
        'hash': utils.hash_sha256(file_data),
        'source_type': models.SourceType.FILE,
    })
    await SharedConfig().file_manager.save_file(
        get_storage_dataset_file_name(ctx.current_org().name, dataset.name, file_db),
        file_data,
    )
    task = asyncio.create_task(manager.index_file_for_extraction(
        file_data, ext, file_db, mode=mode, timeline_id=timeline_id
    ))

    return ORJSONResponse(content=file_db.to_dict())


async def replace_file_extractor(
    extractor_id: int,
    file_id: str,
    file: Optional[UploadFile],
    mode: Optional[Literal['llm', 'ocr']] = 'llm',
):
    ws = ctx.current_workspace()

    async with base.session_context():
        ext = await _get_extractor(extractor_id, edit=True)
        dataset = await db_api.get_dataset_by_org_and_name(ctx.current_org().id, ext.get_dataset_index()[0])
        manager = await EngineManager.init_async(ctx.current_org(), ws, ext)

        file_data = file.file.read()
        file_db = await db_api.get_dataset_file(file_id)
        await SharedConfig().file_manager.delete_file(
            get_storage_dataset_file_name(ctx.current_org().name, dataset.name, file_db),
        )
        await SharedConfig().file_manager.save_file(
            get_storage_dataset_file_name(ctx.current_org().name, dataset.name, file_db),
            file_data,
        )

        await db_api.update_dataset_file(file_db, {'status': utils.build_status(models.STATUS_PROCESSING)})

    task = asyncio.create_task(manager.index_file_for_extraction(file_data, ext, file_db, mode=mode))

    return ORJSONResponse(content=file_db.to_dict())


async def list_files_extractor(
    extractor_id: int,
    limit: Optional[int] = 100,
    page: Optional[int] = 1,
    order: Optional[str] = 'id',
    desc: Optional[bool] = False,
    q: Optional[str] = None,
):
    async with base.session_context():
        ext = await _get_extractor(extractor_id, edit=False)
        dataset = await db_api.get_dataset_by_org_and_name(ctx.current_org().id, ext.get_dataset_index()[0])

        files, count = await db_api.list_dataset_files(dataset.id, limit=limit, page=page, order=order, desc=desc, q=q)
    file_dicts = [f.to_dict() for f in files]

    return ORJSONResponse(content=file_dicts)


async def get_file_extractor(extractor_id: int, file_id: str):
    ext = await _get_extractor(extractor_id, edit=False)

    file_db = await db_api.get_dataset_file(file_id)
    return ORJSONResponse(content=file_db.to_dict())


async def get_file_extractor_by_hash(extractor_id: int, hash_id: str):
    ext = await _get_extractor(extractor_id, edit=False)

    dataset = await db_api.get_dataset_by_org_and_name(ctx.current_org().id, ext.get_dataset_index()[0])
    file_db = await db_api.get_dataset_file_by_hash(hash_id, dataset_ids=[dataset.id])
    if not file_db:
        return Response(content='null')
    else:
        return ORJSONResponse(content=file_db.to_dict())


async def get_file_extractor_contents(extractor_id: int, file_id: str):
    ws = ctx.current_workspace()

    ext = await _get_extractor(extractor_id, edit=False)

    file_db = await db_api.get_dataset_file(file_id)
    manager = await EngineManager.init_async(ctx.current_org(), ws, app=ext)
    docs = await manager.get_index_contents(file_db)
    doc_dicts = [d.dict() for d in docs]
    return ORJSONResponse(content=doc_dicts)


async def delete_file_extractor(extractor_id: int, file_id: str):
    ws = ctx.current_workspace()

    async with base.session_context():
        ext = await _get_extractor(extractor_id, edit=True)
        dataset = await db_api.get_dataset_by_org_and_name(ctx.current_org().id, ext.get_dataset_index()[0])

        try:
            file_db = await db_api.get_dataset_file(file_id)
        except Exception as e:
            file_db = None

        if file_db:
            manager = await EngineManager.init_async(ctx.current_org(), ws, app=ext)

            await manager.delete_embeddings_extractor(file_db)
            await db_api.delete_dataset_file(file_db.id)
            storage_name = get_storage_dataset_file_name(ctx.current_org().name, dataset.name, file_db)
            await SharedConfig().file_manager.delete_file(storage_name)
            await db_api.update_extractor(ext, {'has_updates': True})

    return Response(status_code=204)


async def reindex_file_extractor(extractor_id: int, file_id: str):
    ws = ctx.current_workspace()

    ext = await _get_extractor(extractor_id, edit=True)
    dataset = await db_api.get_dataset_by_org_and_name(ctx.current_org().id, ext.get_dataset_index()[0])

    file_db = await db_api.get_dataset_file(file_id)
    manager = await EngineManager.init_async(ctx.current_org(), ws, app=ext)
    storage_name = get_storage_dataset_file_name(ctx.current_org().name, dataset.name, file_db)

    await manager.delete_embeddings_extractor(file_db)
    read_file = await SharedConfig().file_manager.read_file(storage_name)
    file_data = await SharedConfig().file_manager.get_data(read_file)
    file_db = await db_api.update_dataset_file(file_db, {'status': utils.build_status(models.STATUS_PROCESSING)})
    task = asyncio.create_task(manager.index_file_for_extraction(file_data, ext, file_db))

    return ORJSONResponse(content=file_db.to_dict())


async def download_file_extractor(extractor_id: int, file_id: str, inline: Optional[bool] = False):
    ext = await _get_extractor(extractor_id, edit=False)
    dataset = await db_api.get_dataset_by_org_and_name(ctx.current_org().id, ext.get_dataset_index()[0])

    file_db = await db_api.get_dataset_file(file_id)
    storage_name = get_storage_dataset_file_name(ctx.current_org().name, dataset.name, file_db)

    media_type = mimetypes.guess_type(storage_name)[0]
    if not media_type:
        media_type = 'application/octet-stream'

    data = await SharedConfig().file_manager.read_file(storage_name)
    disposition = 'inline' if inline else 'attachment'
    file_name = file_db.name.encode('latin-1', 'replace').decode('latin-1')

    return StreamingResponse(
        content=SharedConfig().file_manager.get_data_generator(data),
        media_type=media_type,
        headers={'content-length': str(file_db.size), 'Content-Disposition': f'{disposition}; filename="{file_name}"'}
    )


async def create_extractor_item(
    extractor_id: int,
    req: ExtractorItemCreate,
):
    ses = ctx.current_session()
    ws = ctx.current_workspace()

    ext = await db_api.get_extractor_by_id(extractor_id)
    all_chat_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_MANAGE)
    if ses.user_id != ext.owner_id and not all_chat_allowed:
        raise HTTPException(403, 'Must be the owner to create a extractor item')

    ext_config = ext.get_config()

    input = req.input
    if not input:
        input = ext.schema
        ext_config['is_schema'] = True

    if req.is_schema:
        # If the input is a schema, we need to ensure it is valid JSON
        try:
            input = json.loads(input)
            ext_config['is_schema'] = True
        except json.JSONDecodeError as e:
            raise HTTPException(400, f'Invalid JSON schema provided: {str(e)}')

    manager = await EngineManager.init_async(ctx.current_org(), ws, app=ext)
    extractor_item: models.ExtractorItem = await db_api.create_extractor_item({
        'owner_id': ses.user_id,
        'workspace_id': ws.id,
        'extractor_id': extractor_id,
        'input': input,
        'status': models.STATUS_PROCESSING,
    })

    extractor_out: models.ExtractorItemOutput = await db_api.create_extractor_item_output({
        'workspace_id': ws.id,
        'extractor_id': extractor_id,
        'extractor_item_id': extractor_item.id,
        'status': models.STATUS_PROCESSING,
    })

    t1 = asyncio.create_task(manager.process_extractor_request(extractor_item, extractor_out, ctx.request.value.state.access_type))
    await metric_utils.create_metric(
        ctx.current_org().id, ws.id, app_id=ext.id, type_=models.METRIC_EXTRACTOR_ITEM,
        app_type=models.APP_TYPE_EXTRACTOR, obj_id=extractor_out.id,
    )
    extractor_item_dict = extractor_item.to_dict()
    extractor_item_dict['results'] = [extractor_out.to_dict(skip_ids=True)]

    return ORJSONResponse(content=extractor_item_dict)


async def list_extractor_items(
    extractor_id: int,
    limit: Optional[int] = 50,
    page: Optional[int] = 1,
    order: Optional[str] = 'id',
    desc: Optional[bool] = False,
):
    see_all = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_VIEW)
    ws = ctx.current_workspace()

    if not ws:
        raise HTTPException(400, 'Workspace is not set')

    ses = ctx.current_session()
    params = dict(workspace_id=ws.id, extractor_id=extractor_id)
    if not see_all:
        params['owner_id'] = ses.user_id

    extractor = await db_api.get_extractor_by_id(extractor_id)
    if not extractor or extractor.workspace_id != ws.id:
        raise HTTPException(404, f'Detection not found by id: {extractor_id}')

    params.update({
        'limit': limit,
        'page': page,
        'order': order,
        'desc': desc,
    })

    items, count = await db_api.list_extractor_items(**params)
    owner_ids = [t.owner_id for t in items]
    extractor_item_ids = [t.id for t in items]
    users = await db_api.list_users(owner_ids)
    outputs = await db_api.list_extractor_item_outputs(extractor_item_ids=extractor_item_ids)

    outputs_by_extractor = collections.defaultdict(list)
    [outputs_by_extractor[o.extractor_item_id].append(o.to_dict(skip_ids=True)) for o in outputs]
    user_map = {u.id: u for u in users}

    extractor_dicts = [c.to_dict() for c in items]
    for extractor_dict in extractor_dicts:
        extractor_dict['owner_login'] = 'unknown'
        extractor_dict['files'] = []
        # extractor_dict.pop('config', None)

        if extractor_dict['owner_id'] in user_map:
            extractor_dict['owner_login'] = user_map[extractor_dict['owner_id']].login

        if extractor_dict['id'] in outputs_by_extractor:
            extractor_dict['results'] = outputs_by_extractor[extractor_dict['id']]

    result = {'items': extractor_dicts, 'count': count, 'page': page, 'limit': limit}
    return ORJSONResponse(content=result)


async def get_extractor_item(extractor_id: int, item_id: int):
    ses = ctx.current_session()
    extractor_item = await db_api.get_extractor_item_by_id(item_id)

    if extractor_item.extractor_id != extractor_id:
        raise HTTPException(404, f'Not found for id: {item_id}')

    all_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_VIEW)
    if ses.user_id != extractor_item.owner_id and not all_allowed:
        raise HTTPException(403, 'Must be the owner to get')

    owner = await db_api.get_user_by_id(extractor_item.owner_id, notfoundok=True)
    item_dict = extractor_item.to_dict()
    item_dict['owner_login'] = owner.login if owner else 'unknown'

    outputs = await db_api.list_extractor_item_outputs(extractor_item_id=item_id)
    item_dict['results'] = [o.to_dict(skip_ids=True) for o in outputs]

    return ORJSONResponse(content=item_dict)


async def delete_extractor_item(extractor_id: int, item_id: int):
    ses = ctx.current_session()
    extractor_item = await db_api.get_extractor_item_by_id(item_id)
    all_allowed = policies.is_allowed(ctx.current_permissions(), policies.AccessMode.ALL_CHATS_MANAGE)
    if ses.user_id != extractor_item.owner_id and not all_allowed:
        raise HTTPException(403, 'Must be the owner to delete')

    if extractor_id != extractor_item.extractor_id:
        raise HTTPException(404, f'Not found for id: {item_id}')

    await db_api.delete_extractor_item_outputs(extractor_item_id=item_id)
    await db_api.delete_extractor_item(item_id)

    return Response(status_code=204)
