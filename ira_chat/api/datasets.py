import asyncio
import collections
import logging
import mimetypes
import os.path
from typing import Optional, Literal

from fastapi import APIRouter, HTTPException
from fastapi import UploadFile, Form
from fastapi.responses import ORJSONResponse, Response, StreamingResponse
from pydantic import BaseModel

from ira_chat.config.shared_config import SharedConfig
from ira_chat.context import context as ctx
from ira_chat.db import api as db_api, models, base
from ira_chat.policies import policies
from ira_chat.services import dataset_manager, jobs, engine_manager, dataset_utils
from ira_chat.services.dataset_manager import get_storage_dataset_file_name
from ira_chat.utils import utils
from ira_chat.utils.utils import build_status

logger = logging.getLogger(__name__)


class DatasetModel(BaseModel):
    name: str
    display_name: Optional[str] = None
    description: Optional[str] = None
    config: Optional[dict] = None


class UpdateDatasetModel(BaseModel):
    display_name: Optional[str] = None
    description: Optional[str] = None
    config: Optional[dict] = None


class IndexModel(BaseModel):
    name: str
    display_name: Optional[str] = None
    description: Optional[str] = None
    embedding_provider: str
    config: Optional[dict] = None

    index_existing_files: Optional[bool] = False


class UpdateIndexModel(BaseModel):
    display_name: Optional[str] = None
    description: Optional[str] = None
    config: Optional[dict] = None


class ActionIndexModel(BaseModel):
    action: Literal['lock', 'unlock', 'sync']


def get_router(prefix='/'):
    router = APIRouter(prefix=os.path.join(prefix, 'datasets'))

    router.add_api_route("", list_datasets, methods=['GET'], name='List datasets')
    router.add_api_route("/default_prompt_config", get_default_prompt_config, methods=['GET'], name='List datasets')
    router.add_api_route("", create_dataset, methods=['POST'], name='Create dataset')
    router.add_api_route("/{dataset_id}", get_dataset, methods=['GET'], name='Get dataset')
    router.add_api_route("/{dataset_id}", update_dataset, methods=['PUT'], name='Update dataset')
    router.add_api_route("/{dataset_id}", delete_dataset, methods=['DELETE'], name='Delete dataset')
    router.add_api_route("/{dataset_id}/reprocess", reprocess_all, methods=['PUT'], name='Reindex all files')

    router.add_api_route("/{dataset_id}/files", list_dataset_files, methods=['GET'], name='List dataset files')
    router.add_api_route("/{dataset_id}/files", upload_dataset_file, methods=['POST'], name='Create dataset file')
    router.add_api_route("/{dataset_id}/files/{file_id}", get_dataset_file, methods=['GET'], name='Get dataset file')
    router.add_api_route("/{dataset_id}/files/{file_id}", update_dataset_file, methods=['PUT'], name='Update dataset file')
    router.add_api_route("/{dataset_id}/files/{file_id}", delete_dataset_file, methods=['DELETE'], name='Delete dataset file')
    router.add_api_route("/{dataset_id}/files/{file_id}/download", download_file, methods=['GET'], name='Download file')
    router.add_api_route("/{dataset_id}/files/{file_id}/reindex", reindex_file, methods=['PUT'], name='Reindex file')

    # Indexes
    router.add_api_route("/{dataset_id}/indexes", list_dataset_indexes, methods=['GET'], name='List dataset indexes')
    router.add_api_route("/{dataset_id}/indexes", create_dataset_index, methods=['POST'], name='Create dataset index')
    router.add_api_route("/{dataset_id}/indexes/{index_id}", get_dataset_index, methods=['GET'], name='Get dataset index')
    router.add_api_route("/{dataset_id}/indexes/{index_id}", update_dataset_index, methods=['PUT'], name='Update dataset index')
    router.add_api_route("/{dataset_id}/indexes/{index_id}/action", action_dataset_index, methods=['PUT'], name='Action dataset index')
    router.add_api_route("/{dataset_id}/search", search_in_dataset, methods=['GET'], name='Search in first index in dataset')
    router.add_api_route("/{dataset_id}/indexes/{index_id}/search", search_in_dataset_index, methods=['GET'], name='Search in dataset index')
    router.add_api_route("/{dataset_id}/indexes/{index_id}", delete_dataset_index, methods=['DELETE'], name='Delete dataset index')

    return router


async def list_datasets(
    limit: Optional[int] = 100,
    page: Optional[int] = 1,
    order: Optional[str] = 'id',
    desc: Optional[bool] = False,
    scope: Optional[str] = None,
    owner_id: Optional[int] = None,
    q: Optional[str] = None,
):
    _basic_access()
    async with base.session_context():
        org_id = ctx.current_org().id
        datasets, count = await db_api.list_datasets(
            org_id=org_id,
            limit=limit,
            page=page,
            order=order,
            desc=desc,
            scope=scope,
            owner_id=owner_id,
            q=q,
        )
        indexes = await db_api.list_dataset_indexes(dataset_ids=[f.id for f in datasets])
        index_map = collections.defaultdict(list)
        [index_map[idx.dataset_id].append(idx.to_dict()) for idx in indexes]

        dataset_dicts = [c.to_dict() for c in datasets]

        allowed_manage = policies.is_allowed(ctx.current_org_permissions(), policies.OrgAccessMode.DATASET_MANAGE, policies.OrgAccessMode)
        if allowed_manage:
            owners = await db_api.list_users(list(set([f.owner_id for f in datasets])))
            owner_map = {o.id: o.login for o in owners}
            for dataset_dict in dataset_dicts:
                dataset_dict['owner_login'] = owner_map.get(dataset_dict['owner_id'])

        for dataset_dict in dataset_dicts:
            dataset_dict['indexes'] = index_map.get(dataset_dict['id'], [])

    result = {"items": dataset_dicts, "count": count, "page": page, "limit": limit}
    return ORJSONResponse(content=result, status_code=200)


async def get_default_prompt_config():
    manager = dataset_manager.DatasetManager(ctx.current_org(), None)
    return ORJSONResponse(manager.get_default_prompts())


async def create_dataset(create_req: DatasetModel):
    if not policies.is_allowed(ctx.current_org_permissions(), policies.OrgAccessMode.DATASET_MANAGE, policies.OrgAccessMode):
        raise HTTPException(403, 'Forbidden')

    ses = ctx.current_session()
    utils.validate_dataset_name(create_req.name)

    dataset_dict = create_req.model_dump(exclude_unset=True)
    dataset_dict.update({
        'owner_id': ses.user_id,
        'total_size': 0,
        'org_id': ctx.current_org().id,
        'status': {'status': models.STATUS_SUCCESS, 'message': None},
        'config': create_req.config or {},
    })

    dataset = await db_api.create_dataset(dataset_dict)

    dataset_dict = dataset.to_dict()
    dataset_dict['owner_login'] = ctx.current_user().login if ctx.current_user() else None

    return ORJSONResponse(content=dataset_dict, status_code=200)


async def _get_dataset(dataset_id: str, edit=True) -> models.Dataset:
    org = ctx.current_org()
    if edit:
        if not policies.is_allowed(ctx.current_org_permissions(), policies.OrgAccessMode.DATASET_MANAGE, policies.OrgAccessMode):
            raise HTTPException(403, 'Forbidden')
    else:
        _basic_access()

    dataset_db = await db_api.get_dataset(dataset_id)

    if dataset_db.org_id != org.id:
        raise HTTPException(400, f"Dataset not found for id: {dataset_id}")
    
    return dataset_db


async def get_dataset(dataset_id: str):
    async with base.session_context():
        dataset = await _get_dataset(dataset_id, edit=False)

        indexes = await db_api.list_dataset_indexes(dataset_id=dataset.id)
        workspaces_used = await db_api.list_workspaces_using_dataset(dataset_name=dataset.name)
        dataset_dict = dataset.to_dict()
        dataset_dict['indexes'] = [i.to_dict() for i in indexes]
        dataset_dict['used_in_workspaces'] = [w.to_dict(exclude_config=True) for w in workspaces_used]

        allowed_manage = policies.is_allowed(ctx.current_org_permissions(), policies.OrgAccessMode.DATASET_MANAGE, policies.OrgAccessMode)
        if allowed_manage:
            owner = await db_api.get_user_by_id(dataset.owner_id, notfoundok=True)
            dataset_dict['owner_login'] = owner.login if owner else None

    return ORJSONResponse(content=dataset_dict, status_code=200)


async def update_dataset(
    dataset_id: str,
    update_req: UpdateDatasetModel,
):
    dataset = await _get_dataset(dataset_id)
    update_kwargs = update_req.model_dump(exclude_unset=True)
    
    if not update_kwargs:
        raise HTTPException(400, 'Provide data to update')

    dataset = await db_api.update_dataset(dataset, update_kwargs)

    owner = await db_api.get_user_by_id(dataset.owner_id, notfoundok=True)
    dataset_dict = dataset.to_dict()
    dataset_dict['owner_login'] = owner.login if owner else None

    return ORJSONResponse(content=dataset_dict, status_code=200)


async def delete_dataset(dataset_id: str):
    dataset = await _get_dataset(dataset_id)

    dataset = await db_api.update_dataset(dataset, {'status': {'status': models.STATUS_DELETING, 'message': None}})
    t = asyncio.create_task(clear_dataset(dataset.id))

    return ORJSONResponse(content=dataset.to_dict(), status_code=200)


@jobs.job_tracker
async def clear_dataset(dataset_id: str):
    async with base.session_context():
        files = await db_api.list_dataset_files(dataset_id=dataset_id)
        indexes = await db_api.list_dataset_indexes(dataset_id=dataset_id)
        for index in indexes:
            if index.is_locked():
                raise HTTPException(400, f'Index {index.name} is locked, unlock it first.')

            for index in indexes:
                await delete_dataset_index(dataset_id, index.id)
        for file in files:
            await delete_dataset_file(dataset_id, file.id)

        await db_api.delete_dataset(dataset_id)
        logger.info(f'Dataset [id={dataset_id}] deleted successfully.')


async def reprocess_all():
    if not policies.is_allowed(ctx.current_org_permissions(), policies.OrgAccessMode.DATASET_MANAGE, policies.OrgAccessMode):
        raise HTTPException(403, 'Forbidden')

    ws = ctx.current_workspace()
    datasets, _ = await db_api.list_datasets(workspace_id=ws.id)
    org = ctx.current_org()

    updated_datasets = []
    for dataset in datasets:
        dataset = await db_api.update_dataset(
            dataset,
            {'status': {'status': models.STATUS_PROCESSING, 'message': None}}
        )
        updated_datasets.append(dataset)

    # t = asyncio.create_task(reindex_datasets(org, ws, datasets))
    datasets_dict = [f.to_dict() for f in updated_datasets]

    return ORJSONResponse(content=datasets_dict, status_code=200)


async def list_dataset_indexes(
    dataset_id: str,
    limit: Optional[int] = 100,
    page: Optional[int] = 1,
    order: Optional[str] = 'id',
    desc: Optional[bool] = False,
):
    async with base.session_context():
        dataset = await _get_dataset(dataset_id, edit=False)
        indexes, count = await db_api.list_dataset_indexes(
            dataset_id=dataset.id,
            limit=limit,
            page=page,
            order=order,
            desc=desc,
        )
        index_dicts = [i.to_dict() for i in indexes]

        allowed_manage = policies.is_allowed(ctx.current_org_permissions(), policies.OrgAccessMode.DATASET_MANAGE, policies.OrgAccessMode)
        if allowed_manage:
            owners = await db_api.list_users(list(set([i.owner_id for i in indexes])))
            owner_map = {o.id: o.login for o in owners}
            for index_dict in index_dicts:
                index_dict['owner_login'] = owner_map.get(index_dict['owner_id'])

    result = {'items': index_dicts, 'count': count, 'page': page, 'limit': limit}
    return ORJSONResponse(content=result, status_code=200)


async def get_dataset_index(dataset_id: str, index_id: str):
    async with base.session_context():
        dataset = await _get_dataset(dataset_id, edit=False)
        index = await db_api.get_dataset_index(index_id)

        if index.dataset_id != dataset.id:
            raise HTTPException(400, f"Index not found for id: {index_id}")

        index_dict = index.to_dict()

        allowed_manage = policies.is_allowed(ctx.current_org_permissions(), policies.OrgAccessMode.DATASET_MANAGE, policies.OrgAccessMode)
        if allowed_manage:
            owner = await db_api.get_user_by_id(index.owner_id, notfoundok=True)
            index_dict['owner_login'] = owner.login if owner else None

    return ORJSONResponse(content=index_dict, status_code=200)


async def create_dataset_index(dataset_id: str, create_req: IndexModel):
    ses = ctx.current_session()
    dataset = await _get_dataset(dataset_id)

    utils.validate_dataset_name(create_req.name)
    index_dict = create_req.model_dump(exclude_unset=True)
    index_existing_files = index_dict.pop('index_existing_files', False)
    index_dict.update({
        'owner_id': ses.user_id,
        'dataset_id': dataset.id,
        'org_id': ctx.current_org().id,
        'status': {'status': models.STATUS_SUCCESS, 'message': None},
        'total_size': 0,
        'config': create_req.config or {},
    })
    if not index_dict['config'].get('embedding_model'):
        index_dict['config'].pop('embedding_model', None)

    index = await db_api.create_dataset_index(index_dict)
    manager = dataset_manager.DatasetManager(ctx.current_org(), dataset)
    await manager.handle_new_index(index, index_existing_files=index_existing_files)

    index_dict = index.to_dict()
    index_dict['owner_login'] = ctx.current_user().login if ctx.current_user() else None

    return ORJSONResponse(content=index_dict, status_code=200)


async def update_dataset_index(dataset_id: str, index_id: str, update_req: UpdateIndexModel):
    index, dataset = await _get_dataset_index(dataset_id, index_id)
    update_kwargs = update_req.model_dump(exclude_unset=True)

    if not update_kwargs:
        raise HTTPException(400, 'Provide data to update')

    if 'config' in update_kwargs:
        if not update_kwargs['config'].get('embedding_model'):
            update_kwargs['config'].pop('embedding_model', None)

        # Don't allow changing use_unified_collection, collection_type, or vector_dimension
        for key in ['use_unified_collection', 'collection_type', 'vector_dimension', 'workspace_id', 'workspace_name']:
            update_kwargs['config'].pop(key, None)

    index = await db_api.update_dataset_index(index, update_kwargs)

    owner = await db_api.get_user_by_id(index.owner_id, notfoundok=True)
    index_dict = index.to_dict()
    index_dict['owner_login'] = owner.login if owner else None

    return ORJSONResponse(content=index_dict, status_code=200)


async def action_dataset_index(dataset_id: str, index_id: str, action_req: ActionIndexModel):
    index, dataset = await _get_dataset_index(dataset_id, index_id)

    if action_req.action == 'lock':
        await db_api.update_dataset_index(index, {'locked_at': base.now()})
    elif action_req.action == 'unlock':
        await db_api.update_dataset_index(index, {'locked_at': None})
    elif action_req.action == 'sync':
        if index.is_locked():
            raise HTTPException(400, 'Index is locked')
        manager = dataset_manager.DatasetManager(ctx.current_org(), dataset)
        await manager.handle_existing_index(index)

    owner = await db_api.get_user_by_id(index.owner_id, notfoundok=True)
    index_dict = index.to_dict()
    index_dict['owner_login'] = owner.login if owner else None

    return ORJSONResponse(content=index_dict, status_code=200)


async def search_in_dataset(
    dataset_id: str,
    query: str,
    limit: Optional[int] = 10
):
    dataset = await _get_dataset(dataset_id, edit=False)

    indexes = await db_api.list_dataset_indexes(dataset_id=dataset.id)
    if not indexes:
        raise HTTPException(400, 'No indexes found for dataset')

    index = indexes[0]
    result = await search_in_dataset_index_internal(dataset, index, query, limit=limit)
    result_dicts = [r.dict() for r in result]

    return ORJSONResponse(content={'documents': result_dicts})


async def search_in_dataset_index(
    dataset_id: str,
    index_id: str,
    query: str,
    limit: Optional[int] = 10
):
    index, dataset = await _get_dataset_index(dataset_id, index_id, edit=False)

    result = await search_in_dataset_index_internal(dataset, index, query, limit=limit)
    result_dicts = [r.dict() for r in result]

    return ORJSONResponse(content={'documents': result_dicts})


async def search_in_dataset_index_internal(dataset, index, query, limit: int = 10):
    dataset.config['index'] = f'{dataset.name}/{index.name}'
    manager = await engine_manager.EngineManager.init_async(ctx.current_org(), dataset, dataset)
    result = await manager.search_in_docs(query, limit=limit)
    return result


async def delete_dataset_index(dataset_id: str, index_id: str):
    async with base.session_context():
        index, dataset = await _get_dataset_index(dataset_id, index_id)

        if index.is_locked():
            raise HTTPException(400, 'Index is locked, unlock it first.')

        await db_api.delete_dataset_index(index.id)
        manager = dataset_manager.DatasetManager(ctx.current_org(), dataset)
        await manager.delete_index(index)

    return Response(status_code=204)


async def _get_dataset_index(dataset_id: str, index_id: str, edit=True) -> tuple[models.DatasetIndex, models.Dataset]:
    dataset = await _get_dataset(dataset_id, edit=edit)
    index = await db_api.get_dataset_index(index_id)

    if index.dataset_id != dataset.id:
        raise HTTPException(400, f"Index not found for id: {index_id}")

    return index, dataset


async def list_dataset_files(
    dataset_id: str,
    limit: Optional[int] = 100,
    page: Optional[int] = 1,
    order: Optional[str] = 'id',
    desc: Optional[bool] = False,
    q: Optional[str] = None,
    source_type: Optional[str] = None,
    status: Optional[str] = None,
):
    async with base.session_context():
        dataset = await _get_dataset(dataset_id, edit=False)

        files, count = await db_api.list_dataset_files(
            dataset_id=dataset.id,
            limit=limit,
            page=page,
            order=order,
            desc=desc,
            q=q,
            source_type=source_type,
            status=status,
        )
        file_dicts = [f.to_dict() for f in files]

        allowed_manage = policies.is_allowed(ctx.current_org_permissions(), policies.OrgAccessMode.DATASET_MANAGE, policies.OrgAccessMode)
        if allowed_manage:
            owners = await db_api.list_users(list(set([f.owner_id for f in files])))
            owner_map = {o.id: o.login for o in owners}
            for file_dict in file_dicts:
                file_dict['owner_login'] = owner_map.get(file_dict['owner_id'])

    result = {'items': file_dicts, 'count': count, 'page': page, 'limit': limit}
    return ORJSONResponse(content=result, status_code=200)


async def upload_dataset_file(dataset_id: str, wait: Optional[bool] = False):
    dataset = await _get_dataset(dataset_id)

    ses = ctx.current_session()
    org = ctx.current_org()

    formdata = await ctx.request.value.form()

    if 'file' not in formdata:
        raise HTTPException(400, f'Form-data must include "file"')

    form_file = formdata['file']
    data = form_file.file.read()
    source_type = formdata.get('source_type', models.SourceType.FILE)
    if source_type not in [models.SourceType.FILE, models.SourceType.CORRECTED_TXT]:
        raise HTTPException(400, f'Invalid source_type: {source_type}')

    if form_file.filename and form_file.filename.startswith('provided_answers_'):
        source_type = models.SourceType.CORRECTED_TXT

    file_dict = {
        'owner_id': ses.user_id,
        'name': form_file.filename,
        'size': form_file.size,
        'org_id': org.id,
        'dataset_id': dataset.id,
        'hash': utils.hash_sha256(data),
        'status': {'status': models.STATUS_PROCESSING, 'message': None},
        'source_type': source_type,
    }

    manager = dataset_manager.DatasetManager(org, dataset)
    file_db = await db_api.create_dataset_file(file_dict)
    await SharedConfig().file_manager.save_file(
        get_storage_dataset_file_name(org.name, dataset.name, file_db), data
    )
    del data
    t = asyncio.create_task(manager.process_file_for_all_indexes(file_db))
    if wait:
        await t

    file_dict = file_db.to_dict()
    file_dict['owner_login'] = ctx.current_user().login if ctx.current_user() else None

    async with base.session_context():
        await db_api.update_dataset(dataset, {
            'total_size': await db_api.get_dataset_file_size_sum(dataset.id),
            'file_count': await db_api.get_dataset_file_count(dataset_id=dataset.id),
        })

    return ORJSONResponse(content=file_dict, status_code=200)


async def get_dataset_file(dataset_id: str, file_id: str):
    async with base.session_context():
        dataset = await _get_dataset(dataset_id, edit=False)
        file = await db_api.get_dataset_file(file_id)

        if file.dataset_id != dataset.id:
            raise HTTPException(400, f"File not found for id: {file_id}")

        file_dict = file.to_dict()

        allowed_manage = policies.is_allowed(ctx.current_org_permissions(), policies.OrgAccessMode.DATASET_MANAGE, policies.OrgAccessMode)
        if allowed_manage:
            owner = await db_api.get_user_by_id(file.owner_id, notfoundok=True)
            file_dict['owner_login'] = owner.login if owner else None

    return ORJSONResponse(content=file_dict, status_code=200)


async def update_dataset_file(
    dataset_id: str,
    file_id: str,
    file: Optional[UploadFile] = None,
    description: Optional[str] = Form(None),
    wait: Optional[bool] = False,
):
    org = ctx.current_org()
    dataset = await _get_dataset(dataset_id)
    file_db = await db_api.get_dataset_file(file_id)
    update_dict = {}
    if description:
        update_dict['description'] = description

    manager = dataset_manager.DatasetManager(org, dataset)
    if file:
        data = file.file.read()
        update_dict.update({
            'name': file.filename,
            'size': file.size,
            'status': build_status(models.STATUS_PROCESSING),
            'hash': utils.hash_sha256(data),
        })
        await manager.handle_delete_file(file_db)

        storage_name = get_storage_dataset_file_name(org.name, dataset.name, file_db)
        storage_processed_name = f'{storage_name}.processed'
        await SharedConfig().file_manager.delete_file(storage_name)
        await SharedConfig().file_manager.delete_file(storage_processed_name)

    if not update_dict:
        raise HTTPException(400, 'Provide data to update')

    file_db = await db_api.update_dataset_file(file_db, update_dict)
    if file:
        await SharedConfig().file_manager.save_file(
            get_storage_dataset_file_name(org.name, dataset.name, file_db), data
        )
        del data
        t = asyncio.create_task(manager.process_file_for_all_indexes(file_db))
        if wait:
            await t

        await db_api.update_dataset(dataset, {'total_size': await db_api.get_dataset_file_size_sum(dataset.id)})

    return ORJSONResponse(content=file_db.to_dict(), status_code=200)


async def delete_dataset_file(dataset_id: str, file_id: str):
    org = ctx.current_org()
    dataset = await _get_dataset(dataset_id)

    async with base.session_context() as session:
        file_db = await db_api.get_dataset_file(file_id)

        if file_db.dataset_id != dataset.id:
            raise HTTPException(400, f"File not found for id: {file_id}")

        manager = dataset_manager.DatasetManager(org, dataset)
        await manager.handle_delete_file(file_db)
        await db_api.delete_dataset_file(file_db.id)

        storage_name = get_storage_dataset_file_name(org.name, dataset.name, file_db)
        storage_processed_name = f'{storage_name}.processed'
        await SharedConfig().file_manager.delete_file(storage_name)
        await SharedConfig().file_manager.delete_file(storage_processed_name)

        await session.flush()
        await db_api.update_dataset(dataset, {
            'total_size': await db_api.get_dataset_file_size_sum(dataset.id),
            'file_count': await db_api.get_dataset_file_count(dataset_id=dataset.id),
        })

    return Response(status_code=204)


async def reindex_file(dataset_id: str, file_id: str, wait: Optional[bool] = False):
    dataset = await _get_dataset(dataset_id)
    org = ctx.current_org()

    file_db = await db_api.get_dataset_file(file_id)
    file_db = await db_api.update_dataset_file(
        file_db,
        {'status': {'status': models.STATUS_PROCESSING, 'message': None}}
    )

    manager = dataset_manager.DatasetManager(org, dataset)
    await manager.handle_delete_file(file_db)
    t = asyncio.create_task(manager.process_file_for_all_indexes(
        file_db,
        reprocess=True,
        force_index_recreate=True,
    ))
    if wait:
        await t

    return ORJSONResponse(content=file_db.to_dict(), status_code=200)


async def download_file(dataset_id: str, file_id: str):
    _basic_access()
    async with base.session_context():
        dataset = await _get_dataset(dataset_id, edit=False)
        org = ctx.current_org()

        file = await db_api.get_dataset_file(file_id)

    storage_name = get_storage_dataset_file_name(org.name, dataset.name, file)

    media_type = mimetypes.guess_type(storage_name)[0]
    if not media_type:
        media_type = 'application/octet-stream'
    data = await SharedConfig().file_manager.read_file(storage_name)
    return StreamingResponse(
        content=SharedConfig().file_manager.get_data_generator(data),
        media_type=media_type,
        headers={'content-length': str(file.size), 'Content-Disposition': f'attachment; filename="{file.name}"'}
    )


def _basic_access():
    # user_exists = ctx.current_user() is not None
    user_in_org = ctx.current_org_user() is not None
    if not user_in_org and not ctx.current_session().admin:
        raise HTTPException(403, 'Forbidden')


def get_storage_filename(org_name: str, workspace_name: str, dataset: models.Dataset):
    name, ext = os.path.splitext(dataset.name)
    return f'{org_name}/{workspace_name}/{dataset.workspace_id}/{dataset.name}/{dataset.id}{ext}'
